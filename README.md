# Time Tracker

A time tracking application built with Tauri, React, and PostgreSQL.

[![CI](https://github.com/rburgst/ftt-tauri/actions/workflows/ci.yml/badge.svg)](https://github.com/rburgst/ftt-tauri/actions/workflows/ci.yml)
[![Tests](https://github.com/rburgst/ftt-tauri/actions/workflows/tests.yml/badge.svg)](https://github.com/rburgst/ftt-tauri/actions/workflows/tests.yml)
[![Tauri Build](https://github.com/rburgst/ftt-tauri/actions/workflows/tauri-build.yml/badge.svg)](https://github.com/rburgst/ftt-tauri/actions/workflows/tauri-build.yml)

## Development

### Prerequisites

- Node.js (see `.volta` for version)
- PNPM
- Docker (for PostgreSQL)
- Rust (for Tauri)

### Setup

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

3. Start the database:

```bash
pnpm dev:db-up
```

4. Start the development server:

```bash
pnpm dev
```

## Project Structure

This is a monorepo managed with PNPM workspaces and Turborepo:

- `apps/client`: Tauri + React frontend
- `apps/server`: Node.js backend
- `apps/gen-schema`: Schema generation utilities
- `apps/cache`: Zero cache implementation
- `packages/shared`: Shared code and types

## Code Quality

This project uses:

- **Biome**: For linting and formatting
- **TypeScript**: For type checking
- **Husky**: For Git hooks
- **lint-staged**: For running linters on staged files
- **commitlint**: For enforcing commit message conventions

### Commit Convention

This project follows the [Conventional Commits](https://www.conventionalcommits.org/) specification. See [COMMIT_CONVENTION.md](./COMMIT_CONVENTION.md) for more details.

Example commit messages:

```
feat(client): add new timer component
fix(server): resolve issue with database connection
docs: update README with new installation instructions
```

## Scripts

- `pnpm dev`: Start the development server
- `pnpm build`: Build the application
- `pnpm lint`: Lint the codebase
- `pnpm format`: Format the codebase
- `pnpm format:check`: Check formatting without making changes

## DNS

https://server.focustimetracker.app/api/health

## Continuous Integration

This project uses GitHub Actions for continuous integration:

- **CI Workflow**: Compiles, lints, and checks formatting for all code
- **Tests Workflow**: Runs unit tests with a PostgreSQL service container
- **Tauri Build Workflow**: Builds the Tauri application for macOS, Windows, and Linux

GitHub Actions automatically run on push to the main branch and on pull requests. Tagged releases (starting with 'v') will create GitHub Releases with built application binaries.

## TODOs

[x] time rounding
[x] import timesheet projects
[x] query DB
[x] dont use AM/PM, always set secs to 0
[x] better time editor for worklog editor
[ ] fix export server test
[ ] fix creating task for taskcatalog
[ ] enable linking task for taskcatalog
[x] enable linking task to taskcatalog from task detail
[ ] cannot edit comment while editing a recording task