{"name": "@ftt/shared", "version": "1.0.0", "description": "", "main": "src/index.ts", "types": "src/index.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 0", "compile": "tsc --noEmit", "lint": "biome lint .", "format": "biome format --write .", "format:check": "biome format ."}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "dependencies": {"@rocicorp/zero": "catalog:"}, "devDependencies": {"typescript": "~5.8.3"}}