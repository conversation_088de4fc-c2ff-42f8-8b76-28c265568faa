/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// biome-ignore-all
/*
 * ------------------------------------------------------------
 * ## This file was automatically generated by drizzle-zero  ##
 * ## Any changes you make to this file will be overwritten. ##
 * ##                                                        ##
 * ## Additionally, you should also exclude this file from   ##
 * ## your linter and/or formatter to prevent it from being  ##
 * ## checked or modified.                                   ##
 * ##                                                        ##
 * ## SOURCE: https://github.com/BriefHQ/drizzle-zero        ##
 * ------------------------------------------------------------
 */

import type { default as DrizzleConfigSchema } from "./drizzle-zero.config";

/**
 * The Zero schema object.
 * This type is auto-generated from your Drizzle schema definition.
 */
export const schema = {
  tables: {
    appState: {
      name: "appState",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["id"]["customType"],
          serverName: "user_id",
        },
        isEditDialogOpen: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["isEditDialogOpen"]["customType"],
          serverName: "is_edit_dialog_open",
        },
        editDialogTimeRecordId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["editDialogTimeRecordId"]["customType"],
          serverName: "edit_dialog_time_record_id",
        },
        timestampOfIdleTimeStart: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["timestampOfIdleTimeStart"]["customType"],
          serverName: "timestamp_of_idle_time_start",
        },
        showingTimeoutMessage: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["showingTimeoutMessage"]["customType"],
          serverName: "showing_timeout_message",
        },
        runningTimerId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["runningTimerId"]["customType"],
          serverName: "running_timer_id",
        },
        activeClientId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["activeClientId"]["customType"],
          serverName: "active_client_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["appState"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
      },
      primaryKey: ["id"],
      serverName: "app_state",
    },
    customers: {
      name: "customers",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["name"]["customType"],
        },
        rateValue: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["rateValue"]["customType"],
          serverName: "rate_value",
        },
        rateCurrency: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["rateCurrency"]["customType"],
          serverName: "rate_currency",
        },
        timeNormalizationType: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["timeNormalizationType"]["customType"],
          serverName: "time_normalization_type",
        },
        timeNormalizationConfig: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["timeNormalizationConfig"]["customType"],
          serverName: "time_normalization_config",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["customers"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "customer",
    },
    failedSyncs: {
      name: "failedSyncs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["id"]["customType"],
        },
        userId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["userId"]["customType"],
          serverName: "user_id",
        },
        timerecordId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["timerecordId"]["customType"],
          serverName: "timerecord_id",
        },
        uploadData: {
          type: "json",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["uploadData"]["customType"],
          serverName: "upload_data",
        },
        error: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["error"]["customType"],
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["failedSyncs"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "failed_syncs",
    },
    projectCatalogs: {
      name: "projectCatalogs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["name"]["customType"],
        },
        key: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["key"]["customType"],
        },
        remoteId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["remoteId"]["customType"],
          serverName: "remote_id",
        },
        remoteUrl: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["remoteUrl"]["customType"],
          serverName: "remote_url",
        },
        remoteServiceId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["remoteServiceId"]["customType"],
          serverName: "remote_service_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projectCatalogs"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "project_catalog",
    },
    projects: {
      name: "projects",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["name"]["customType"],
        },
        customerId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["customerId"]["customType"],
          serverName: "customer_id",
        },
        projectCatalogId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["projectCatalogId"]["customType"],
          serverName: "project_catalog_id",
        },
        color: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["color"]["customType"],
        },
        timeNormalizationType: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["timeNormalizationType"]["customType"],
          serverName: "time_normalization_type",
        },
        timeNormalizationConfig: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["timeNormalizationConfig"]["customType"],
          serverName: "time_normalization_config",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["projects"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "project",
    },
    queries: {
      name: "queries",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["name"]["customType"],
        },
        position: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["position"]["customType"],
        },
        predicate: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["predicate"]["customType"],
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["queries"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "query",
    },
    remoteServices: {
      name: "remoteServices",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["name"]["customType"],
        },
        serviceType: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["serviceType"]["customType"],
          serverName: "service_type",
        },
        remoteUrl: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["remoteUrl"]["customType"],
          serverName: "remote_url",
        },
        remoteUser: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["remoteUser"]["customType"],
          serverName: "remote_user",
        },
        remotePassword: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["remotePassword"]["customType"],
          serverName: "remote_password",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["remoteServices"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "remote_service",
    },
    taskCatalogs: {
      name: "taskCatalogs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["name"]["customType"],
        },
        key: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["key"]["customType"],
        },
        status: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["status"]["customType"],
        },
        remoteId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["remoteId"]["customType"],
          serverName: "remote_id",
        },
        remoteUrl: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["remoteUrl"]["customType"],
          serverName: "remote_url",
        },
        projectCatalogId: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["projectCatalogId"]["customType"],
          serverName: "project_catalog_id",
        },
        lastUsed: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["lastUsed"]["customType"],
          serverName: "last_used",
        },
        pinned: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["pinned"]["customType"],
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskCatalogs"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "task_catalog",
    },
    taskToTaskCatalogs: {
      name: "taskToTaskCatalogs",
      columns: {
        taskId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["taskId"]["customType"],
          serverName: "task_id",
        },
        taskCatalogId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["taskCatalogId"]["customType"],
          serverName: "task_catalog_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["taskToTaskCatalogs"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["taskId", "taskCatalogId"],
      serverName: "task_to_task_catalogs",
    },
    tasks: {
      name: "tasks",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["name"]["customType"],
        },
        projectId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["projectId"]["customType"],
          serverName: "project_id",
        },
        status: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["status"]["customType"],
        },
        defaultTask: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["defaultTask"]["customType"],
          serverName: "default_task",
        },
        pinned: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["pinned"]["customType"],
        },
        lastUsed: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["lastUsed"]["customType"],
          serverName: "last_used",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["tasks"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "task",
    },
    timerecordToTaskCatalogs: {
      name: "timerecordToTaskCatalogs",
      columns: {
        timerecordId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["timerecordId"]["customType"],
          serverName: "timerecord_id",
        },
        taskCatalogId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["taskCatalogId"]["customType"],
          serverName: "task_catalog_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecordToTaskCatalogs"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["timerecordId", "taskCatalogId"],
      serverName: "timerecord_to_task_catalogs",
    },
    timerecords: {
      name: "timerecords",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["id"]["customType"],
        },
        taskId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["taskId"]["customType"],
          serverName: "task_id",
        },
        startTimestamp: {
          type: "number",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["startTimestamp"]["customType"],
          serverName: "start_timestamp",
        },
        endTimestamp: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["endTimestamp"]["customType"],
          serverName: "end_timestamp",
        },
        comment: {
          type: "string",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["comment"]["customType"],
        },
        uploadData: {
          type: "json",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["uploadData"]["customType"],
          serverName: "upload_data",
        },
        uploadSuccess: {
          type: "boolean",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["uploadSuccess"]["customType"],
          serverName: "upload_success",
        },
        uploaded: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["uploaded"]["customType"],
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timerecords"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "timerecord",
    },
    timers: {
      name: "timers",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["id"]["customType"],
        },
        userId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["userId"]["customType"],
          serverName: "user_id",
        },
        startTimestamp: {
          type: "number",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["startTimestamp"]["customType"],
          serverName: "start_timestamp",
        },
        endTimestamp: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["endTimestamp"]["customType"],
          serverName: "end_timestamp",
        },
        status: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["status"]["customType"],
        },
        worklogId: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["worklogId"]["customType"],
          serverName: "timerecord_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["createdAt"]["customType"],
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["updatedAt"]["customType"],
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["createdBy"]["customType"],
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["timers"]["columns"]["updatedBy"]["customType"],
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "timer",
    },
    users: {
      name: "users",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["users"]["columns"]["id"]["customType"],
        },
        name: {
          type: "string",
          optional: false,
          customType:
            null as (typeof DrizzleConfigSchema)["tables"]["users"]["columns"]["name"]["customType"],
        },
      },
      primaryKey: ["id"],
      serverName: "user",
    },
  },
  relationships: {
    appState: {
      user: [
        {
          sourceField: ["id"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      runningTimer: [
        {
          sourceField: ["runningTimerId"],
          destField: ["id"],
          destSchema: "timers",
          cardinality: "one",
        },
      ],
      editDialogTimeRecord: [
        {
          sourceField: ["editDialogTimeRecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
    },
    customers: {
      projects: [
        {
          sourceField: ["id"],
          destField: ["customerId"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    failedSyncs: {
      user: [
        {
          sourceField: ["userId"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerecord: [
        {
          sourceField: ["timerecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    projectCatalogs: {
      remoteService: [
        {
          sourceField: ["remoteServiceId"],
          destField: ["id"],
          destSchema: "remoteServices",
          cardinality: "one",
        },
      ],
      projects: [
        {
          sourceField: ["id"],
          destField: ["projectCatalogId"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    projects: {
      customer: [
        {
          sourceField: ["customerId"],
          destField: ["id"],
          destSchema: "customers",
          cardinality: "one",
        },
      ],
      projectCatalog: [
        {
          sourceField: ["projectCatalogId"],
          destField: ["id"],
          destSchema: "projectCatalogs",
          cardinality: "one",
        },
      ],
      tasks: [
        {
          sourceField: ["id"],
          destField: ["projectId"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    queries: {
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    remoteServices: {
      projectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["remoteServiceId"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    taskCatalogs: {
      projectCatalog: [
        {
          sourceField: ["projectCatalogId"],
          destField: ["id"],
          destSchema: "projectCatalogs",
          cardinality: "one",
        },
      ],
      taskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskCatalogId"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      timerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskCatalogId"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    taskToTaskCatalogs: {
      task: [
        {
          sourceField: ["taskId"],
          destField: ["id"],
          destSchema: "tasks",
          cardinality: "one",
        },
      ],
      taskCatalog: [
        {
          sourceField: ["taskCatalogId"],
          destField: ["id"],
          destSchema: "taskCatalogs",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    tasks: {
      project: [
        {
          sourceField: ["projectId"],
          destField: ["id"],
          destSchema: "projects",
          cardinality: "one",
        },
      ],
      taskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskId"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      timerecords: [
        {
          sourceField: ["id"],
          destField: ["taskId"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timerecordToTaskCatalogs: {
      timerecord: [
        {
          sourceField: ["timerecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
      taskCatalog: [
        {
          sourceField: ["taskCatalogId"],
          destField: ["id"],
          destSchema: "taskCatalogs",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timerecords: {
      task: [
        {
          sourceField: ["taskId"],
          destField: ["id"],
          destSchema: "tasks",
          cardinality: "one",
        },
      ],
      timerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["timerecordId"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timers: {
      timerOwner: [
        {
          sourceField: ["userId"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerCreator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerUpdater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerecord: [
        {
          sourceField: ["worklogId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
    },
    users: {
      createdTimers: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      updatedTimers: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      ownedTimers: [
        {
          sourceField: ["id"],
          destField: ["userId"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      createdTimerecords: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      updatedTimerecords: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      createdCustomers: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "customers",
          cardinality: "many",
        },
      ],
      updatedCustomers: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "customers",
          cardinality: "many",
        },
      ],
      createdRemoteServices: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "remoteServices",
          cardinality: "many",
        },
      ],
      updatedRemoteServices: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "remoteServices",
          cardinality: "many",
        },
      ],
      createdProjectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      updatedProjectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      createdProjects: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      updatedProjects: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      createdTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "taskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "taskCatalogs",
          cardinality: "many",
        },
      ],
      createdTasks: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      updatedTasks: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      createdTaskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTaskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      createdTimerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTimerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      createdQueries: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "queries",
          cardinality: "many",
        },
      ],
      updatedQueries: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "queries",
          cardinality: "many",
        },
      ],
      createdFailedSyncs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
      updatedFailedSyncs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
      failedSyncUser: [
        {
          sourceField: ["id"],
          destField: ["userId"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
    },
  },
} as const;

/**
 * Represents the Zero schema type.
 * This type is auto-generated from your Drizzle schema definition.
 */
export type Schema = typeof schema;
