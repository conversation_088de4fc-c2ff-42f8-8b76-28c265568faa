# Commit Message Convention

This project follows the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages.

## Format

Each commit message consists of a **header**, a **body**, and a **footer**:

```
<type>(<scope>): <subject>

<body>

<footer>
```

The **header** is mandatory and must conform to the [Commit Message Header](#commit-message-header) format.

The **body** is optional but recommended for providing additional context.

The **footer** is optional and can be used to reference issue trackers.

## Commit Message Header

The header has a special format that includes a **type**, an optional **scope**, and a **subject**:

```
<type>(<scope>): <subject>
```

### Type

Must be one of the following:

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, etc.)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **build**: Changes that affect the build system or external dependencies
- **ci**: Changes to our CI configuration files and scripts
- **chore**: Other changes that don't modify src or test files
- **revert**: Reverts a previous commit

### Scope

The scope is optional and should be a noun describing a section of the codebase:

- **client**: Changes to the client application
- **server**: Changes to the server application
- **shared**: Changes to shared code
- **ui**: Changes to UI components
- **db**: Changes to database-related code
- **config**: Changes to configuration files

### Subject

The subject contains a succinct description of the change:

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Don't capitalize the first letter
- No period (.) at the end

## Examples

```
feat(client): add new timer component

fix(server): resolve issue with database connection

docs: update README with new installation instructions

style: format code according to new Biome rules

refactor(shared): simplify authentication logic

test(api): add tests for user endpoints

chore: update dependencies
```

## Tools

This project uses [commitlint](https://commitlint.js.org/) to enforce this convention. The commit-msg hook will check your commit messages and reject them if they don't follow the convention.
