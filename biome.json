{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["**/node_modules/**", "**/dist/**", "**/.turbo/**", "**/*.gen.ts", "**/*.gen.js", "data/**", "**/components/ui/**"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "error"}, "correctness": {"noUnusedImports": "error", "noUnusedVariables": "error", "useExhaustiveDependencies": "error"}, "nursery": {"useGoogleFontDisplay": "error", "noDocumentImportInPage": "error", "noHeadElement": "error", "noHeadImportInDocument": "error", "noImgElement": "error"}, "style": {"noNonNullAssertion": "warn", "useShorthandArrayType": "error"}}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded"}}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}}