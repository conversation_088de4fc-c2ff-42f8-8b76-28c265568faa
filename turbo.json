{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env"], "tasks": {"compile": {"dependsOn": ["^compile"], "outputs": [], "inputs": ["**/*.ts", "**/*.tsx", "tsconfig.json"]}, "build": {"dependsOn": ["^build", "compile"], "outputs": ["dist/**"]}, "dev": {"dependsOn": ["^compile"], "cache": false, "persistent": true}, "lint": {"dependsOn": [], "outputs": [], "cache": true, "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "biome.json"]}, "lint:eslint": {"dependsOn": [], "outputs": [], "cache": true, "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "eslint.config.js"]}, "lint:all": {"dependsOn": ["lint", "lint:eslint"], "outputs": [], "cache": true}, "format": {"dependsOn": [], "outputs": [], "cache": false, "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "biome.json"]}, "format:check": {"dependsOn": [], "outputs": [], "cache": true, "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "biome.json"]}, "test": {"dependsOn": ["^build"], "outputs": [], "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.test.{ts,tsx,js,jsx}"]}}}