# From https://github.com/vercel/next.js/blob/canary/examples/with-docker/Dockerfile

FROM node:22-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

FROM base AS pnpm
RUN npm install -g corepack@latest && corepack enable pnpm && pnpm --version

# Install dependencies only when needed
FROM pnpm AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store corepack enable pnpm && pnpm i --frozen-lockfile; 


# Rebuild the source code only when needed
FROM pnpm AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
#COPY .env.dockerbuild .env
#COPY .env.dockerbuild .env.production
#RUN echo "DOCKERBUILD" && cat .env
#RUN echo "DOCKERBUILD.prod" && cat .env.production

RUN --mount=type=cache,id=pnpm,target=/pnpm/store corepack enable pnpm && cd apps/server && pnpm i --frozen-lockfile && pnpm run build

# Production image, copy all the files and run next
FROM pnpm AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/apps/server/drizzle ./drizzle

COPY --from=builder --chown=nextjs:nodejs /app/apps/server/dist/index.js .
COPY --from=builder --chown=nextjs:nodejs /app/apps/server/src/pg-cloudflare.js src/pg-cloudflare.js
COPY  ./scripts/start-server.sh .
COPY apps/server/package.json /app/
COPY packages/shared/src/schema* /app/
RUN chmod a+x start-server.sh

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["./start-server.sh"]
