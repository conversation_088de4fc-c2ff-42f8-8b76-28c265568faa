name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    name: Build, Lin<PERSON>, and Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.15.0'
      
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
      
      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: '10.10.0'
      
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT
      
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      
      - name: Setup Rust cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            apps/client/src-tauri/target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Compile
        run: pnpm compile
      
      - name: Lint
        run: pnpm lint
      
      - name: Format check
        run: pnpm format:check
      
      - name: Build client
        working-directory: ./apps/client
        run: pnpm build
      
      - name: Build server
        working-directory: ./apps/server
        run: pnpm build
      
      - name: Run client tests
        working-directory: ./apps/client
        run: |
          if grep -q "\"test\":" package.json && ! grep -q "\"test\": \"echo \\\"Error: no test specified\\\" && exit 1\"," package.json; then
            pnpm test
          else
            echo "No tests configured for client, skipping"
          fi
      
      - name: Run server tests
        working-directory: ./apps/server
        run: |
          if grep -q "\"test\":" package.json && ! grep -q "\"test\": \"echo \\\"Error: no test specified\\\" && exit 1\"," package.json; then
            pnpm test
          else
            echo "No tests configured for server, skipping"
          fi
