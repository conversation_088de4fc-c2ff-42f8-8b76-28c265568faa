name: Tauri Build

on:
  push:
    # branches: [main]
    tags:
      - "v*"
  # pull_request:
  #   branches: [main]

jobs:
  build:
    name: Build Tauri App (${{ matrix.platform }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        # platform: [macos-latest, ubuntu-latest, windows-latest]
        platform: [ubuntu-latest]
        include:
          # - platform: macos-latest
          #   os: macos-latest
          #   target: universal-apple-darwin
          - platform: ubuntu-latest
            os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          # - platform: windows-latest
          #   os: windows-latest
          #   target: x86_64-pc-windows-msvc

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22.15.0"

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: "10.10.0"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Setup Rust cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            apps/client/src-tauri/target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-

      # - name: Install dependencies (ubuntu only)
      #   if: matrix.platform == 'ubuntu-latest'
      #   run: |
      #     sudo apt-get update
      #     sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install dependencies
        run: pnpm install

      - name: Build Tauri App
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          projectPath: apps/client
          tauriScript: pnpm tauri
          args: build --target ${{ matrix.target }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tauri-artifacts-${{ matrix.platform }}
          path: |
            apps/client/src-tauri/target/release/bundle/dmg/*.dmg
            apps/client/src-tauri/target/release/bundle/deb/*.deb
            apps/client/src-tauri/target/release/bundle/appimage/*.AppImage
            apps/client/src-tauri/target/release/bundle/msi/*.msi
            apps/client/src-tauri/target/release/bundle/nsis/*.exe
          if-no-files-found: ignore

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/v')
        with:
          files: |
            apps/client/src-tauri/target/release/bundle/dmg/*.dmg
            apps/client/src-tauri/target/release/bundle/deb/*.deb
            apps/client/src-tauri/target/release/bundle/appimage/*.AppImage
            apps/client/src-tauri/target/release/bundle/msi/*.msi
            apps/client/src-tauri/target/release/bundle/nsis/*.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
