name: Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16.2-alpine
        env:
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: postgres
        ports:
          - 5430:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22.15.0"

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: "10.10.0"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Set up environment variables
        run: |
          echo "DATABASE_URL=postgresql://user:password@localhost:5430/postgres" >> $GITHUB_ENV
          echo "ZERO_UPSTREAM_DB=postgresql://user:password@localhost:5430/postgres" >> $GITHUB_ENV
          echo "ZERO_AUTH_SECRET=test-secret" >> $GITHUB_ENV

      - name: Run client tests with Vitest
        working-directory: ./apps/client
        run: |
          if grep -q "vitest" package.json; then
            pnpm exec vitest run --coverage
          else
            echo "Vitest not configured for client, skipping"
          fi

      - name: Run server tests
        working-directory: ./apps/server
        run: |
          if grep -q "\"test\":" package.json && ! grep -q "\"test\": \"echo \\\"Error: no test specified\\\" && exit 1\"," package.json; then
            pnpm test --coverage
          else
            echo "No tests configured for server, skipping"
          fi

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            apps/client/coverage/
            apps/server/coverage/
          if-no-files-found: ignore
