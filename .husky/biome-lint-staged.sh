#!/bin/sh

# This script is used by lint-staged to run Biome on staged files
# It takes a command (check or format) and a list of files as arguments

command=$1
shift
files=$@

# log on stderr
echo "Running biome $command on $files" > .biome-log.log

# check if the list of files is empty
if [ -z "$files" ]; then
  echo "No files to check"
  exit 0
fi

if [ "$command" = "check" ]; then
  pnpm biome check $files
elif [ "$command" = "format" ]; then
  pnpm biome format --write $files
else
  echo "Unknown command: $command"
  exit 1
fi
