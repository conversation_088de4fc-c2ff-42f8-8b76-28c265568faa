# Git Hooks

This directory contains Git hooks managed by [<PERSON><PERSON>](https://typicode.github.io/husky/) v9+.

> **Note:** These hooks use the new Husky v9+ format which doesn't require the shell script header.
> All hooks use pnpm to run scripts, ensuring we use the versions of tools installed in the project.

## Available Hooks

- **pre-commit**: Runs Biome to check and format staged files before committing
- **commit-msg**: Validates commit messages using commitlint to ensure they follow the [Conventional Commits](https://www.conventionalcommits.org/) format
- **post-checkout**: Automatically runs `pnpm install` when package.json or pnpm-lock.yaml changes

## Pre-commit Hook

The pre-commit hook uses lint-staged with a Node.js helper script to run Biome on staged files:

- Runs Biome checks and formatting on JavaScript/TypeScript files
- Formats JSON and Markdown files with Biome

This approach uses the `scripts/lint-staged-biome.js` helper script which handles passing the staged files to Biome in a reliable way.

### Alternative Approaches

#### Shell Script Helper

If you prefer using lint-staged, there's a helper shell script `.husky/biome-lint-staged.sh` that can be used with lint-staged. To switch to this approach, modify the pre-commit hook to use lint-staged and update the lint-staged configuration in package.json to use the helper script.

#### Node.js Helper

For more robust file handling, there's also a Node.js helper script `scripts/lint-staged-biome.js` that uses the `--files-from-file` option in Biome. This approach is more reliable for handling large numbers of files or files with special characters in their paths.

To use this approach, update the lint-staged configuration in package.json:

```json
"lint-staged": {
  "*.{js,jsx,ts,tsx}": [
    "node scripts/lint-staged-biome.js check",
    "node scripts/lint-staged-biome.js format"
  ],
  "*.{json,md}": ["node scripts/lint-staged-biome.js format"]
}
```

And modify the pre-commit hook to use lint-staged:

```sh
pnpm lint-staged
```

## Commit Message Convention

This project uses [commitlint](https://commitlint.js.org/) to enforce the [Conventional Commits](https://www.conventionalcommits.org/) format for commit messages. See the `COMMIT_CONVENTION.md` file in the root directory for more details.

Example commit messages:

```
feat(client): add new timer component
fix(server): resolve issue with database connection
docs: update README with new installation instructions
```

## Skipping Hooks

In rare cases when you need to bypass hooks, you can use the `--no-verify` flag:

```bash
git commit -m "Your message" --no-verify
```

However, this should be used sparingly as the hooks are in place to maintain code quality.
