import { drizzleZeroConfig } from 'drizzle-zero'
import * as drizzleSchema from './drizzle/drizzle-schema'

// Define your configuration file for the CLI
export default drizzleZeroConfig(drizzleSchema, {
  // Specify which tables and columns to include in the Zero schema.
  // This allows for the "expand/migrate/contract" pattern recommended in the Zero docs.
  // When a column is first added, it should be set to false, and then changed to true
  // once the migration has been run.

  // All tables/columns must be defined, but can be set to false to exclude them from the Zero schema.
  // Column names match your Drizzle schema definitions
  tables: {
    // this can be set to false
    // e.g. user: false,
    users: {
      id: true,
      name: true,
    },
    timers: {
      id: true,
      worklogId: true,
      userId: true,
      startTimestamp: true,
      endTimestamp: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    timerecords: {
      id: true,
      taskId: true,
      startTimestamp: true,
      endTimestamp: true,
      comment: true,
      uploadData: true,
      uploadSuccess: true,
      uploaded: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    customers: {
      id: true,
      name: true,
      rateValue: true,
      rateCurrency: true,
      timeNormalizationType: true,
      timeNormalizationConfig: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    remoteServices: {
      id: true,
      name: true,
      serviceType: true,
      remoteUrl: true,
      remoteUser: true,
      remotePassword: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    projectCatalogs: {
      id: true,
      name: true,
      key: true,
      remoteId: true,
      remoteUrl: true,
      remoteServiceId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    projects: {
      id: true,
      name: true,
      customerId: true,
      projectCatalogId: true,
      color: true,
      timeNormalizationType: true,
      timeNormalizationConfig: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    taskCatalogs: {
      id: true,
      name: true,
      key: true,
      status: true,
      remoteId: true,
      remoteUrl: true,
      projectCatalogId: true,
      lastUsed: true,
      pinned: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    tasks: {
      id: true,
      name: true,
      projectId: true,
      // catalogRefId: true,
      defaultTask: true,
      pinned: true,
      lastUsed: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    queries: {
      id: true,
      name: true,
      position: true,
      predicate: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    appState: {
      id: true,
      isEditDialogOpen: true,
      timestampOfIdleTimeStart: true,
      showingTimeoutMessage: true,
      runningTimerId: true,
      activeClientId: true,
      editDialogTimeRecordId: true,
      createdAt: true,
      updatedAt: true,
    },
    taskToTaskCatalogs: {
      taskId: true,
      taskCatalogId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    timerecordToTaskCatalogs: {
      timerecordId: true,
      taskCatalogId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
    failedSyncs: {
      id: true,
      userId: true,
      timerecordId: true,
      uploadData: true,
      error: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
    },
  },
})
