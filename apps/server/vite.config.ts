import build from '@hono/vite-build/node'
import { defineConfig } from 'vite'
// import build from '@hono/vite-build/cloudflare-pages'
// import build from '@hono/vite-build/cloudflare-workers'
// import build from '@hono/vite-build/node'
// import build from '@hono/vite-build/netlify-functions'
// import build from '@hono/vite-build/vercel'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  plugins: [
    build({
      entry: './src/index.ts',
      // port option is only for Node.js adapter. Default is 3000
      port: 3000,
    }),
    tsconfigPaths(),
  ],
  environments: {
    ssr: {
      keepProcessEnv: true,
    },
  },
  build: {
    rollupOptions: {
      external: ['pg-cloudflare', 'cloudflare:sockets'],
      output: {
        paths: {
          'pg-cloudflare': './src/pg-cloudflare.js',
        },
      },
    },
    minify: false,
  },
})
