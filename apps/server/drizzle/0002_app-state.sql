CREATE TABLE "app_state" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"is_edit_dialog_open" boolean DEFAULT false NOT NULL,
	"timestamp_of_idle_time_start" timestamp,
	"showing_timeout_message" boolean DEFAULT false NOT NULL,
	"running_timer_id" uuid
);
--> statement-breakpoint
ALTER TABLE "app_state" ADD CONSTRAINT "app_state_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "app_state" ADD CONSTRAINT "app_state_running_timer_id_timer_id_fk" FOREIGN KEY ("running_timer_id") REFERENCES "public"."timer"("id") ON DELETE no action ON UPDATE no action;