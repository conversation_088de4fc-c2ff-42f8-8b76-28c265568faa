ALTER TABLE "app_state" DROP CONSTRAINT "app_state_user_id_user_id_fk";
--> statement-breakpoint
ALTER TABLE "customer" DROP CONSTRAINT "customer_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "customer" DROP CONSTRAINT "customer_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "failed_syncs" DROP CONSTRAINT "failed_syncs_user_id_user_id_fk";
--> statement-breakpoint
ALTER TABLE "failed_syncs" DROP CONSTRAINT "failed_syncs_timerecord_id_timerecord_id_fk";
--> statement-breakpoint
ALTER TABLE "failed_syncs" DROP CONSTRAINT "failed_syncs_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "failed_syncs" DROP CONSTRAINT "failed_syncs_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "project_catalog" DROP CONSTRAINT "project_catalog_remote_service_id_remote_service_id_fk";
--> statement-breakpoint
ALTER TABLE "project_catalog" DROP CONSTRAINT "project_catalog_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "project_catalog" DROP CONSTRAINT "project_catalog_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "project" DROP CONSTRAINT "project_customer_id_customer_id_fk";
--> statement-breakpoint
ALTER TABLE "project" DROP CONSTRAINT "project_project_catalog_id_project_catalog_id_fk";
--> statement-breakpoint
ALTER TABLE "project" DROP CONSTRAINT "project_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "project" DROP CONSTRAINT "project_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "query" DROP CONSTRAINT "query_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "query" DROP CONSTRAINT "query_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "remote_service" DROP CONSTRAINT "remote_service_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "remote_service" DROP CONSTRAINT "remote_service_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task_catalog" DROP CONSTRAINT "task_catalog_project_catalog_id_project_catalog_id_fk";
--> statement-breakpoint
ALTER TABLE "task_catalog" DROP CONSTRAINT "task_catalog_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task_catalog" DROP CONSTRAINT "task_catalog_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" DROP CONSTRAINT "task_to_task_catalogs_task_id_task_id_fk";
--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" DROP CONSTRAINT "task_to_task_catalogs_task_catalog_id_task_catalog_id_fk";
--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" DROP CONSTRAINT "task_to_task_catalogs_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" DROP CONSTRAINT "task_to_task_catalogs_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "task_project_id_project_id_fk";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "task_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "task_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" DROP CONSTRAINT "timerecord_to_task_catalogs_timerecord_id_timerecord_id_fk";
--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" DROP CONSTRAINT "timerecord_to_task_catalogs_task_catalog_id_task_catalog_id_fk";
--> statement-breakpoint
ALTER TABLE "timerecord" DROP CONSTRAINT "timerecord_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "timerecord" DROP CONSTRAINT "timerecord_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "timer" DROP CONSTRAINT "timer_user_id_user_id_fk";
--> statement-breakpoint
ALTER TABLE "timer" DROP CONSTRAINT "timer_timerecord_id_timerecord_id_fk";
--> statement-breakpoint
ALTER TABLE "timer" DROP CONSTRAINT "timer_created_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "timer" DROP CONSTRAINT "timer_updated_by_user_id_fk";
--> statement-breakpoint
ALTER TABLE "app_state" ADD CONSTRAINT "app_state_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer" ADD CONSTRAINT "customer_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer" ADD CONSTRAINT "customer_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "failed_syncs" ADD CONSTRAINT "failed_syncs_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "failed_syncs" ADD CONSTRAINT "failed_syncs_timerecord_id_timerecord_id_fk" FOREIGN KEY ("timerecord_id") REFERENCES "public"."timerecord"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "failed_syncs" ADD CONSTRAINT "failed_syncs_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "failed_syncs" ADD CONSTRAINT "failed_syncs_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project_catalog" ADD CONSTRAINT "project_catalog_remote_service_id_remote_service_id_fk" FOREIGN KEY ("remote_service_id") REFERENCES "public"."remote_service"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project_catalog" ADD CONSTRAINT "project_catalog_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project_catalog" ADD CONSTRAINT "project_catalog_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project" ADD CONSTRAINT "project_customer_id_customer_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customer"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project" ADD CONSTRAINT "project_project_catalog_id_project_catalog_id_fk" FOREIGN KEY ("project_catalog_id") REFERENCES "public"."project_catalog"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project" ADD CONSTRAINT "project_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project" ADD CONSTRAINT "project_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "query" ADD CONSTRAINT "query_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "query" ADD CONSTRAINT "query_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "remote_service" ADD CONSTRAINT "remote_service_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "remote_service" ADD CONSTRAINT "remote_service_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_catalog" ADD CONSTRAINT "task_catalog_project_catalog_id_project_catalog_id_fk" FOREIGN KEY ("project_catalog_id") REFERENCES "public"."project_catalog"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_catalog" ADD CONSTRAINT "task_catalog_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_catalog" ADD CONSTRAINT "task_catalog_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_task_id_task_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."task"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_task_catalog_id_task_catalog_id_fk" FOREIGN KEY ("task_catalog_id") REFERENCES "public"."task_catalog"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "task_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "task_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "task_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_timerecord_id_timerecord_id_fk" FOREIGN KEY ("timerecord_id") REFERENCES "public"."timerecord"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_task_catalog_id_task_catalog_id_fk" FOREIGN KEY ("task_catalog_id") REFERENCES "public"."task_catalog"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timerecord" ADD CONSTRAINT "timerecord_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timerecord" ADD CONSTRAINT "timerecord_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timer" ADD CONSTRAINT "timer_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timer" ADD CONSTRAINT "timer_timerecord_id_timerecord_id_fk" FOREIGN KEY ("timerecord_id") REFERENCES "public"."timerecord"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timer" ADD CONSTRAINT "timer_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "timer" ADD CONSTRAINT "timer_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;