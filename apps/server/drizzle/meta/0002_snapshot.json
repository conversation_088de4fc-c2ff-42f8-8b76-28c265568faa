{"id": "57cd3d58-76e1-4c28-a7fa-2ed9e466a601", "prevId": "1acd5c29-9ad3-45eb-aac4-0e7e3603b55c", "version": "7", "dialect": "postgresql", "tables": {"public.app_state": {"name": "app_state", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_edit_dialog_open": {"name": "is_edit_dialog_open", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "timestamp_of_idle_time_start": {"name": "timestamp_of_idle_time_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "showing_timeout_message": {"name": "showing_timeout_message", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "running_timer_id": {"name": "running_timer_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"app_state_user_id_user_id_fk": {"name": "app_state_user_id_user_id_fk", "tableFrom": "app_state", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "app_state_running_timer_id_timer_id_fk": {"name": "app_state_running_timer_id_timer_id_fk", "tableFrom": "app_state", "tableTo": "timer", "columnsFrom": ["running_timer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer": {"name": "customer", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "rate_value": {"name": "rate_value", "type": "numeric", "primaryKey": false, "notNull": false}, "rate_currency": {"name": "rate_currency", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_type": {"name": "time_normalization_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_config": {"name": "time_normalization_config", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"customer_created_by_user_id_fk": {"name": "customer_created_by_user_id_fk", "tableFrom": "customer", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "customer_updated_by_user_id_fk": {"name": "customer_updated_by_user_id_fk", "tableFrom": "customer", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_catalog": {"name": "project_catalog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_id": {"name": "remote_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_service_id": {"name": "remote_service_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_catalog_remote_service_id_remote_service_id_fk": {"name": "project_catalog_remote_service_id_remote_service_id_fk", "tableFrom": "project_catalog", "tableTo": "remote_service", "columnsFrom": ["remote_service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_catalog_created_by_user_id_fk": {"name": "project_catalog_created_by_user_id_fk", "tableFrom": "project_catalog", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_catalog_updated_by_user_id_fk": {"name": "project_catalog_updated_by_user_id_fk", "tableFrom": "project_catalog", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_catalog_id": {"name": "project_catalog_id", "type": "uuid", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_type": {"name": "time_normalization_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_config": {"name": "time_normalization_config", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_customer_id_customer_id_fk": {"name": "project_customer_id_customer_id_fk", "tableFrom": "project", "tableTo": "customer", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_project_catalog_id_project_catalog_id_fk": {"name": "project_project_catalog_id_project_catalog_id_fk", "tableFrom": "project", "tableTo": "project_catalog", "columnsFrom": ["project_catalog_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_created_by_user_id_fk": {"name": "project_created_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_updated_by_user_id_fk": {"name": "project_updated_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.query": {"name": "query", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "numeric", "primaryKey": false, "notNull": false}, "predicate": {"name": "predicate", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"query_created_by_user_id_fk": {"name": "query_created_by_user_id_fk", "tableFrom": "query", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "query_updated_by_user_id_fk": {"name": "query_updated_by_user_id_fk", "tableFrom": "query", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.remote_service": {"name": "remote_service", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_user": {"name": "remote_user", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_password": {"name": "remote_password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"remote_service_created_by_user_id_fk": {"name": "remote_service_created_by_user_id_fk", "tableFrom": "remote_service", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "remote_service_updated_by_user_id_fk": {"name": "remote_service_updated_by_user_id_fk", "tableFrom": "remote_service", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_catalog": {"name": "task_catalog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_id": {"name": "remote_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "project_catalog_id": {"name": "project_catalog_id", "type": "uuid", "primaryKey": false, "notNull": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "pinned": {"name": "pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"task_catalog_project_catalog_id_project_catalog_id_fk": {"name": "task_catalog_project_catalog_id_project_catalog_id_fk", "tableFrom": "task_catalog", "tableTo": "project_catalog", "columnsFrom": ["project_catalog_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_catalog_created_by_user_id_fk": {"name": "task_catalog_created_by_user_id_fk", "tableFrom": "task_catalog", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_catalog_updated_by_user_id_fk": {"name": "task_catalog_updated_by_user_id_fk", "tableFrom": "task_catalog", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task": {"name": "task", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "catalog_ref_id": {"name": "catalog_ref_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "task_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "default_task": {"name": "default_task", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "pinned": {"name": "pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"task_project_id_project_id_fk": {"name": "task_project_id_project_id_fk", "tableFrom": "task", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_catalog_ref_id_task_catalog_id_fk": {"name": "task_catalog_ref_id_task_catalog_id_fk", "tableFrom": "task", "tableTo": "task_catalog", "columnsFrom": ["catalog_ref_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_created_by_user_id_fk": {"name": "task_created_by_user_id_fk", "tableFrom": "task", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_updated_by_user_id_fk": {"name": "task_updated_by_user_id_fk", "tableFrom": "task", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timerecord": {"name": "timerecord", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_timestamp": {"name": "start_timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_timestamp": {"name": "end_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "remote_issue_id": {"name": "remote_issue_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_worklog_id": {"name": "remote_worklog_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "upload_success": {"name": "upload_success", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "uploaded": {"name": "uploaded", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"timerecord_task_id_task_id_fk": {"name": "timerecord_task_id_task_id_fk", "tableFrom": "timerecord", "tableTo": "task", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timerecord_created_by_user_id_fk": {"name": "timerecord_created_by_user_id_fk", "tableFrom": "timerecord", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timerecord_updated_by_user_id_fk": {"name": "timerecord_updated_by_user_id_fk", "tableFrom": "timerecord", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timer": {"name": "timer", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_timestamp": {"name": "start_timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_timestamp": {"name": "end_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "timer_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "timerecord_id": {"name": "timerecord_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"timer_user_id_user_id_fk": {"name": "timer_user_id_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timer_timerecord_id_timerecord_id_fk": {"name": "timer_timerecord_id_timerecord_id_fk", "tableFrom": "timer", "tableTo": "timerecord", "columnsFrom": ["timerecord_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timer_created_by_user_id_fk": {"name": "timer_created_by_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timer_updated_by_user_id_fk": {"name": "timer_updated_by_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.task_status": {"name": "task_status", "schema": "public", "values": ["OPEN", "CLOSED", "CHARGED"]}, "public.timer_status": {"name": "timer_status", "schema": "public", "values": ["running", "stopped"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}