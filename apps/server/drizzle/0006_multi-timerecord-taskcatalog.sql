CREATE TABLE "timerecord_to_task_catalogs" (
	"timerecord_id" uuid NOT NULL,
	"task_catalog_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now () NOT NULL,
	"updated_at" timestamp DEFAULT now () NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_by" uuid NOT NULL,
	CONSTRAINT "timerecord_to_task_catalogs_timerecord_id_task_catalog_id_pk" PRIMARY KEY ("timerecord_id", "task_catalog_id")
);

ALTER TABLE "timerecord"
ADD COLUMN "upload_data" json;

ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_timerecord_id_timerecord_id_fk" FOREIGN KEY ("timerecord_id") REFERENCES "public"."timerecord" ("id") ON DELETE no action ON UPDATE no action;

--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_task_catalog_id_task_catalog_id_fk" FOREIGN KEY ("task_catalog_id") REFERENCES "public"."task_catalog" ("id") ON DELETE no action ON UPDATE no action;

--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user" ("id") ON DELETE no action ON UPDATE no action;

--> statement-breakpoint
ALTER TABLE "timerecord_to_task_catalogs" ADD CONSTRAINT "timerecord_to_task_catalogs_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user" ("id") ON DELETE no action ON UPDATE no action;

--> statement-breakpoint
insert into
	timerecord_to_task_catalogs (
		timerecord_id,
		task_catalog_id,
		created_by,
		updated_by
	)
select
	t.id,
	cast(t.remote_issue_id as uuid),
	cast('c50b8068-23d8-4686-8f0b-e22bd19a5ecd' as uuid),
	cast('c50b8068-23d8-4686-8f0b-e22bd19a5ecd' as uuid)
from
	timerecord t
where
	t.remote_issue_id is not null;

ALTER TABLE "timerecord"
DROP COLUMN "remote_issue_id";

ALTER TABLE "timerecord"
DROP COLUMN "remote_worklog_id";