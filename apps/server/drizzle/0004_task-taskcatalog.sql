CREATE TABLE "task_to_task_catalogs" (
	"task_id" uuid NOT NULL,
	"task_catalog_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_by" uuid NOT NULL,
	CONSTRAINT "task_to_task_catalogs_task_id_task_catalog_id_pk" PRIMARY KEY("task_id","task_catalog_id")
);
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "task_catalog_ref_id_task_catalog_id_fk";
--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_task_id_task_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."task"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_task_catalog_id_task_catalog_id_fk" FOREIGN KEY ("task_catalog_id") REFERENCES "public"."task_catalog"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_to_task_catalogs" ADD CONSTRAINT "task_to_task_catalogs_updated_by_user_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task" DROP COLUMN "catalog_ref_id";