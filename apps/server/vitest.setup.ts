import { afterAll, beforeAll, vi } from 'vitest'

// Set up environment variables for testing
beforeAll(() => {
  console.log('Setting up environment variables for testing...')

  // Mock environment variables
  process.env.ZERO_AUTH_SECRET = 'test-secret'
  process.env.DATABASE_URL = 'postgresql://user:password@localhost:5432/testdb'
  process.env.PORT = '3030'

  // Mock console methods to reduce noise in test output
  vi.spyOn(console, 'log').mockImplementation(() => {})
  vi.spyOn(console, 'info').mockImplementation(() => {})
  vi.spyOn(console, 'warn').mockImplementation(() => {})
  // Keep console.error for debugging test failures
})

afterAll(() => {
  // Restore console methods
  vi.restoreAllMocks()
})
