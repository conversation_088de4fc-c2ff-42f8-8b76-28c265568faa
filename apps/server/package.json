{"name": "@ftt/server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"build": "pnpm compile && vite build", "compile": "tsc --noEmit", "db:migrate": "drizzle-kit migrate", "dev": "tsx watch --env-file=.env src/index.ts", "format": "biome format --write .", "format:check": "biome format  .", "generate": "drizzle-zero generate -f && mv zero-schema.gen.ts ../../packages/shared/src/schema.gen.ts", "g": "pnpm generate", "import-json": "tsx --env-file=.env src/scripts/import-json.ts", "lint": "biome lint .", "migration:create": "drizzle-kit generate", "permissions:deploy": "npx --env-file=.env zero-deploy-permissions -p ../../packages/shared/src/schema.ts", "start": "node --env-file=.env dist/index.js", "studio": "drizzle-kit studio", "test": "vitest run", "test:watch": "vitest", "vite:dev": "vite dev", "export-json": "tsx --env-file=.env src/scripts/export-json.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "dependencies": {"@hono/node-server": "^1.14.1", "@hono/zod-validator": "^0.5.0", "@t3-oss/env-core": "^0.13.4", "drizzle-orm": "^0.43.1", "es-toolkit": "^1.37.2", "hono": "^4.7.9", "jose": "^6.0.11", "pg": "^8.15.6", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@hono/vite-build": "^1.6.1", "@rocicorp/zero": "catalog:", "@types/node": "^22.15.17", "@types/pg": "^8.15.1", "@types/uuid": "^10.0.0", "@vitest/coverage-v8": "^3.1.3", "drizzle-kit": "^0.31.0", "drizzle-zero": "^0.10.1", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3"}, "volta": {"node": "22.14.0"}}