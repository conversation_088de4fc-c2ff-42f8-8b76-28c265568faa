import { createEnv } from '@t3-oss/env-core'
import { z } from 'zod'

// Enhanced debugging for environment variables
console.log('Environment variables debug:')
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('ZERO_AUTH_SECRET exists:', !!process.env.ZERO_AUTH_SECRET)
console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL)
console.log('PORT:', process.env.PORT)

// Create environment configuration with more lenient validation in production
const envConfig = {
  server: {
    ZERO_AUTH_SECRET: z.string().min(1),
    DATABASE_URL: z.string().min(1),
    PORT: z.coerce.number().int().positive().default(3020),
  },
  runtimeEnv: process.env,
  emptyStringAsUndefined: true,
}

// Export the environment variables
export const env = createEnv(envConfig)
