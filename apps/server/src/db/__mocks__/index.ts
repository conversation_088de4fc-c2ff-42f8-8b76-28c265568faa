import { vi } from 'vitest'

// Create a mock database object with common methods
export const db = {
  select: vi.fn().mockReturnThis(),
  from: vi.fn().mockReturnThis(),
  where: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  execute: vi.fn().mockResolvedValue([]),
  insert: vi.fn().mockReturnThis(),
  values: vi.fn().mockReturnThis(),
  returning: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  set: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  // Add more methods as needed
}

// Mock specific query results for tests
export function mockDbQueryResult(result: unknown) {
  db.execute.mockResolvedValueOnce(result)
  return db
}
