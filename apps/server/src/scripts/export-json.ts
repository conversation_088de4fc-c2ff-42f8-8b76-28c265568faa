import fs from 'node:fs'
import path from 'node:path'
import {
  timers,
  customers,
  projectCatalogs,
  projects,
  queries,
  remoteServices,
  taskCatalogs,
  taskToTaskCatalogs,
  tasks,
  timerecordToTaskCatalogs,
  timerecords,
  users,
} from '../../drizzle/drizzle-schema'
import { db } from '../db'

// Function to ensure directory exists
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
  }
}

// Function to write data to JSON file
function writeJsonFile(filePath: string, data: unknown): void {
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8')
}

// Export users
async function exportUsers(outputDir: string): Promise<void> {
  console.log('Exporting users...')
  const usersData = await db.select().from(users)
  writeJsonFile(path.join(outputDir, 'TTNG_USER.json'), usersData)
  console.log(`Exported ${usersData.length} users.`)
}

// Export customers
async function exportCustomers(outputDir: string): Promise<void> {
  console.log('Exporting customers...')
  const customersData = await db.select().from(customers)
  writeJsonFile(path.join(outputDir, 'TTNG_CUSTOMER.json'), customersData)
  console.log(`Exported ${customersData.length} customers.`)
}

// Export remote services
async function exportRemoteServices(outputDir: string): Promise<void> {
  console.log('Exporting remote services...')
  const remoteServicesData = await db.select().from(remoteServices)
  writeJsonFile(
    path.join(outputDir, 'TTNG_REMOTESERVICE.json'),
    remoteServicesData
  )
  console.log(`Exported ${remoteServicesData.length} remote services.`)
}

// Export project catalogs
async function exportProjectCatalogs(outputDir: string): Promise<void> {
  console.log('Exporting project catalogs...')
  const projectCatalogsData = await db.select().from(projectCatalogs)
  writeJsonFile(
    path.join(outputDir, 'TTNG_PROJECTCATALOG.json'),
    projectCatalogsData
  )
  console.log(`Exported ${projectCatalogsData.length} project catalogs.`)
}

// Export task catalogs
async function exportTaskCatalogs(outputDir: string): Promise<void> {
  console.log('Exporting task catalogs...')
  const taskCatalogsData = await db.select().from(taskCatalogs)
  writeJsonFile(path.join(outputDir, 'TTNG_TASKCATALOG.json'), taskCatalogsData)
  console.log(`Exported ${taskCatalogsData.length} task catalogs.`)
}

// Export projects
async function exportProjects(outputDir: string): Promise<void> {
  console.log('Exporting projects...')
  const projectsData = await db.select().from(projects)
  writeJsonFile(path.join(outputDir, 'TTNG_PROJECT.json'), projectsData)
  console.log(`Exported ${projectsData.length} projects.`)
}

// Export tasks
async function exportTasks(outputDir: string): Promise<void> {
  console.log('Exporting tasks...')
  const tasksData = await db.select().from(tasks)
  writeJsonFile(path.join(outputDir, 'TTNG_TASK.json'), tasksData)
  console.log(`Exported ${tasksData.length} tasks.`)

  // Export task to task catalogs relationships
  console.log('Exporting task to task catalog relationships...')
  const taskToTaskCatalogsData = await db.select().from(taskToTaskCatalogs)
  writeJsonFile(
    path.join(outputDir, 'TTNG_TASK_TO_TASKCATALOG.json'),
    taskToTaskCatalogsData
  )
  console.log(
    `Exported ${taskToTaskCatalogsData.length} task to task catalog relationships.`
  )
}

// Export timerecords
async function exportTimerecords(outputDir: string): Promise<void> {
  console.log('Exporting timerecords...')
  const timerecordsData = await db.select().from(timerecords)
  writeJsonFile(path.join(outputDir, 'TTNG_TIMERECORD.json'), timerecordsData)
  console.log(`Exported ${timerecordsData.length} timerecords.`)

  // Export timerecord to task catalogs relationships
  console.log('Exporting timerecord to task catalog relationships...')
  const timerecordToTaskCatalogsData = await db
    .select()
    .from(timerecordToTaskCatalogs)
  writeJsonFile(
    path.join(outputDir, 'TTNG_TIMERECORD_TO_TASKCATALOG.json'),
    timerecordToTaskCatalogsData
  )
  console.log(
    `Exported ${timerecordToTaskCatalogsData.length} timerecord to task catalog relationships.`
  )
}

// Export queries
async function exportQueries(outputDir: string): Promise<void> {
  console.log('Exporting queries...')
  const queriesData = await db.select().from(queries)
  writeJsonFile(path.join(outputDir, 'TTNG_QUERY.json'), queriesData)
  console.log(`Exported ${queriesData.length} queries.`)
}

// Export timers
async function exportTimers(outputDir: string): Promise<void> {
  console.log('Exporting timers...')
  const timersData = await db.select().from(timers)
  writeJsonFile(path.join(outputDir, 'TTNG_TIMER.json'), timersData)
  console.log(`Exported ${timersData.length} timers.`)
}

// Main export function
export async function exportAllData(outputDir: string): Promise<void> {
  console.log(`Starting export to ${outputDir}...`)

  // Ensure the output directory exists
  ensureDirectoryExists(outputDir)

  // Export in the same order as import
  await exportUsers(outputDir)
  await exportCustomers(outputDir)
  await exportRemoteServices(outputDir)
  await exportProjectCatalogs(outputDir)
  await exportTaskCatalogs(outputDir)
  await exportProjects(outputDir)
  await exportTasks(outputDir)
  await exportTimerecords(outputDir)
  await exportQueries(outputDir)
  await exportTimers(outputDir)

  console.log('Export completed successfully!')
  return Promise.resolve()
}

// Run the export if called directly (ES module version)
const isMainModule = import.meta.url === `file://${process.argv[1]}`
if (isMainModule) {
  const outputDir =
    process.argv[2] || path.join(process.cwd(), '../../data/export')
  exportAllData(outputDir)
    .then(() => {
      console.log(`All data exported successfully to ${outputDir}!`)
      process.exit(0)
    })
    .catch((error) => {
      console.error('Error exporting data:', error)
      process.exit(1)
    })
}
