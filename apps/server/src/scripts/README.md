# JSON Data Importer

This directory contains scripts for importing JSON data from the `data/` directory into the database.

## Import JSON Data

The `import-json.ts` script reads JSON files from the `data/` directory and imports them into the database according to the Drizzle schema.

### Usage

To run the importer:

```bash
# From the apps/server directory
pnpm import-json

# Or from the project root
pnpm --filter @ftt/server import-json
```

### Data Files

The importer expects the following JSON files in the `data/` directory:

- `TTNG_CUSTOMER.json` - Customer data
- `TTNG_REMOTESERVICE.json` - Remote service data
- `TTNG_PROJECTCATALOG.json` - Project catalog data
- `TTNG_PROJECT.json` - Project data
- `TTNG_TASK.json` - Task data
- `TTNG_TIMERECORD.json` - Time record data
- `TTNG_TIMERECORD_TO_TASKCATALOG.json` - Time record to task catalog relationships (new format)
- `TTNG_QUERY.json` - Query data
- `TTNG_TIMER.json` - Timer data

The importer supports both the old format (where timerecords have a direct `REMOTEISSUE_ID` field) and the new format (where timerecord to task catalog relationships are stored in a separate file).

### Import Order

The data is imported in a specific order to handle dependencies:

1. Users (creates a default user if none exists)
2. Customers
3. Remote Services
4. Project Catalogs
5. Task Catalogs
6. Projects
7. Tasks
8. Time Records
9. Time Record to Task Catalog Relationships (new format)
10. Queries
11. Timers

### Default User

The importer creates a default user with ID `c50b8068-23d8-4686-8f0b-e22bd19a5ecd` if it doesn't already exist. This user is used for the `createdBy` and `updatedBy` fields in all imported records.

## Troubleshooting

If you encounter issues during import:

1. Make sure the database is running and accessible
2. Check that the JSON files are properly formatted
3. Ensure that the database schema matches the expected structure
4. Look for error messages in the console output
