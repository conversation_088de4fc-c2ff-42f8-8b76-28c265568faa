import fs from 'node:fs'
import path from 'node:path'
import { eq } from 'drizzle-orm'
import { invariant } from 'es-toolkit'
import {
  timers,
  customers,
  projectCatalogs,
  projects,
  queries,
  remoteServices,
  taskCatalogs,
  taskToTaskCatalogs,
  tasks,
  timerecordToTaskCatalogs,
  timerecords,
  users,
} from '../../drizzle/drizzle-schema'
import type {
  NewCustomer,
  NewProject,
  NewProjectCatalog,
  NewQuery,
  NewRemoteService,
  NewTask,
  NewTaskCatalog,
  NewTaskToTaskCatalog,
  NewTimerecord,
  NewTimerecordToTaskCatalog,
  NewUser,
} from '../../drizzle/drizzle-schema'
import { db } from '../db'
// We're using existing IDs from the JSON files, no need to generate new ones

// Default user ID to use for createdBy/updatedBy fields
const DEFAULT_USER_ID = 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd'

// Function to read JSON file
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
function readJsonFile(filePath: string): any[] {
  const data = fs.readFileSync(filePath, 'utf8')
  return JSON.parse(data)
}

// Function to get all JSON files in a directory
function getJsonFiles(dirPath: string): string[] {
  const files = fs.readdirSync(dirPath)
  return files
    .filter((file) => file.endsWith('.json'))
    .map((file) => path.join(dirPath, file))
}

// Function to convert date string to Date object
function parseDate(dateStr: string | null): Date | null {
  if (!dateStr) return null
  return new Date(dateStr)
}

// Import users
async function importUsers(): Promise<void> {
  console.log('Checking if default user exists...')

  // Check if the default user already exists
  const existingUser = await db
    .select()
    .from(users)
    .where(eq(users.id, DEFAULT_USER_ID))

  if (existingUser.length === 0) {
    console.log('Creating default user...')

    // Create default user
    const newUser: NewUser = {
      id: DEFAULT_USER_ID,
      name: 'Default User',
    }

    await db.insert(users).values(newUser)
    console.log('Default user created.')
  } else {
    console.log('Default user already exists.')
  }
}

function mapTimeNormalizationType(type: string | null): string | null {
  if (!type) return null
  switch (type) {
    case 'DEFAULT':
    case 'MINUTE':
      return 'MINUTE'
    case 'NONE':
      return 'NONE'
    default:
      return type
  }
}

// Import customers
async function importCustomers(filePath: string): Promise<void> {
  console.log('Importing customers...')
  const customersData = readJsonFile(filePath)

  for (const customerData of customersData) {
    const newCustomer: NewCustomer = {
      id: customerData.ID,
      name: customerData.NAME,
      rateValue: customerData.RATEVALUE
        ? customerData.RATEVALUE.toString()
        : null,
      rateCurrency: customerData.RATECURRENCY,
      timeNormalizationType: mapTimeNormalizationType(
        customerData.TIMENORMALIZATIONTYPE
      ),
      timeNormalizationConfig: customerData.TIMENORMALIZATIONCONFIG,
      createdAt: parseDate(customerData.CREATED) || new Date(),
      updatedAt: parseDate(customerData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(customers).values(newCustomer).onConflictDoUpdate({
      target: customers.id,
      set: newCustomer,
    })
  }

  console.log(`Imported ${customersData.length} customers.`)
}

// Import remote services
async function importRemoteServices(filePath: string): Promise<void> {
  console.log('Importing remote services...')
  const remoteServicesData = readJsonFile(filePath)

  for (const serviceData of remoteServicesData) {
    const newService: NewRemoteService = {
      id: serviceData.ID,
      name: serviceData.NAME,
      serviceType: serviceData.SERVICETYPE,
      remoteUrl: serviceData.REMOTEURL,
      remoteUser: serviceData.REMOTEUSER,
      remotePassword: serviceData.REMOTEPASSWORD,
      createdAt: parseDate(serviceData.CREATED) || new Date(),
      updatedAt: parseDate(serviceData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(remoteServices).values(newService).onConflictDoUpdate({
      target: remoteServices.id,
      set: newService,
    })
  }

  console.log(`Imported ${remoteServicesData.length} remote services.`)
}

// Import project catalogs
async function importProjectCatalogs(filePath: string): Promise<void> {
  console.log('Importing project catalogs...')
  const catalogsData = readJsonFile(filePath)

  for (const catalogData of catalogsData) {
    const newCatalog: NewProjectCatalog = {
      id: catalogData.ID,
      name: catalogData.NAME,
      key: catalogData.KEY,
      remoteId: catalogData.REMOTEID,
      remoteUrl: catalogData.REMOTEURL,
      remoteServiceId: catalogData.REMOTESERVICE_ID,
      createdAt: parseDate(catalogData.CREATED) || new Date(),
      updatedAt: parseDate(catalogData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(projectCatalogs).values(newCatalog).onConflictDoUpdate({
      target: projectCatalogs.id,
      set: newCatalog,
    })
  }

  console.log(`Imported ${catalogsData.length} project catalogs.`)
}

// Import task catalogs
async function importTaskCatalogs(filePath: string): Promise<void> {
  console.log('Importing task catalogs...')
  const catalogsData = readJsonFile(filePath)

  for (const catalogData of catalogsData) {
    // Check if the project catalog exists if specified
    if (catalogData.PROJECTCATALOG_ID) {
      const projectCatalogExists = await db
        .select({ id: projectCatalogs.id })
        .from(projectCatalogs)
        .where(eq(projectCatalogs.id, catalogData.PROJECTCATALOG_ID))
        .limit(1)

      if (projectCatalogExists.length === 0) {
        throw new Error(
          `Project catalog ${catalogData.PROJECTCATALOG_ID} not found for task catalog ${catalogData.ID}. Import failed.`
        )
      }
    }

    const newCatalog: NewTaskCatalog = {
      id: catalogData.ID,
      name: catalogData.NAME,
      key: catalogData.KEY,
      status: catalogData.STATUS,
      remoteId: catalogData.REMOTEID,
      remoteUrl: catalogData.REMOTEURL,
      projectCatalogId: catalogData.PROJECTCATALOG_ID,
      lastUsed: parseDate(catalogData.LASTUSED),
      pinned: catalogData.PINNED || false,
      createdAt: parseDate(catalogData.CREATED) || new Date(),
      updatedAt: parseDate(catalogData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(taskCatalogs).values(newCatalog).onConflictDoUpdate({
      target: taskCatalogs.id,
      set: newCatalog,
    })
  }

  console.log(`Imported ${catalogsData.length} task catalogs.`)
}

// Import projects
async function importProjects(filePath: string): Promise<void> {
  console.log('Importing projects...')
  const projectsData = readJsonFile(filePath)
  let importedCount = 0

  for (const projectData of projectsData) {
    // Check if the customer exists
    const customerExists = await db
      .select({ id: customers.id })
      .from(customers)
      .where(eq(customers.id, projectData.CUSTOMER_ID))
      .limit(1)

    if (customerExists.length === 0) {
      throw new Error(
        `Customer ${projectData.CUSTOMER_ID} not found for project ${projectData.ID}. Import failed.`
      )
    }

    // If project has a catalog ID, check if it exists
    if (projectData.PROJECTCATALOG_ID) {
      const catalogExists = await db
        .select({ id: projectCatalogs.id })
        .from(projectCatalogs)
        .where(eq(projectCatalogs.id, projectData.PROJECTCATALOG_ID))
        .limit(1)

      if (catalogExists.length === 0) {
        throw new Error(
          `Project catalog ${projectData.PROJECTCATALOG_ID} not found for project ${projectData.ID}. Import failed.`
        )
      }
    }

    const newProject: NewProject = {
      id: projectData.ID,
      name: projectData.NAME,
      customerId: projectData.CUSTOMER_ID,
      projectCatalogId: projectData.PROJECTCATALOG_ID,
      color: projectData.COLOR,
      timeNormalizationType: mapTimeNormalizationType(
        projectData.TIMENORMALIZATIONTYPE
      ),
      timeNormalizationConfig: projectData.TIMENORMALIZATIONCONFIG,
      createdAt: parseDate(projectData.CREATED) || new Date(),
      updatedAt: parseDate(projectData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(projects).values(newProject).onConflictDoUpdate({
      target: projects.id,
      set: newProject,
    })
    importedCount++
  }

  console.log(`Imported ${importedCount} of ${projectsData.length} projects.`)
}

// Import tasks
async function importTasks(filePath: string): Promise<void> {
  console.log('Importing tasks...')
  const tasksData = readJsonFile(filePath)

  for (const taskData of tasksData) {
    // Check if the project exists
    const projectExists = await db
      .select({ id: projects.id })
      .from(projects)
      .where(eq(projects.id, taskData.PROJECT_ID))
      .limit(1)

    if (projectExists.length === 0) {
      throw new Error(
        `Project ${taskData.PROJECT_ID} not found for task ${taskData.ID}. Import failed.`
      )
    }

    // Create the task first
    const newTask: NewTask = {
      id: taskData.ID,
      name: taskData.NAME,
      projectId: taskData.PROJECT_ID,
      status: taskData.TASKSTATUS || 'OPEN',
      defaultTask: taskData.DEFAULTTASK || false,
      pinned: taskData.PINNED || false,
      lastUsed: parseDate(taskData.LASTUSED),
      createdAt: parseDate(taskData.CREATED) || new Date(),
      updatedAt: parseDate(taskData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(tasks).values(newTask).onConflictDoUpdate({
      target: tasks.id,
      set: newTask,
    })

    // If there's a catalog reference, create the many-to-many relationship
    if (taskData.CATALOGREF_ID) {
      // Check if the task catalog exists
      const taskCatalogExists = await db
        .select({ id: taskCatalogs.id })
        .from(taskCatalogs)
        .where(eq(taskCatalogs.id, taskData.CATALOGREF_ID))
        .limit(1)

      if (taskCatalogExists.length === 0) {
        throw new Error(
          `Task catalog ${taskData.CATALOGREF_ID} not found for task ${taskData.ID}. Import failed.`
        )
      }

      // Create the task-to-task-catalog relationship
      const newTaskToTaskCatalog: NewTaskToTaskCatalog = {
        taskId: taskData.ID,
        taskCatalogId: taskData.CATALOGREF_ID,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: DEFAULT_USER_ID,
        updatedBy: DEFAULT_USER_ID,
      }

      // Use onConflictDoNothing to avoid duplicate key errors if the relationship already exists
      await db
        .insert(taskToTaskCatalogs)
        .values(newTaskToTaskCatalog)
        .onConflictDoNothing({
          target: [taskToTaskCatalogs.taskId, taskToTaskCatalogs.taskCatalogId],
        })
    }
  }

  console.log(`Imported ${tasksData.length} tasks.`)
}

// Import timerecords
async function importTimerecords(filePath: string): Promise<void> {
  console.log('Importing timerecords...')
  const timerecordsData = readJsonFile(filePath)
  let importedCount = 0

  // We already checked that the table exists in checkDatabaseSchema()

  for (const recordData of timerecordsData) {
    // Check if the task exists
    const taskExists = await db
      .select({ id: tasks.id })
      .from(tasks)
      .where(eq(tasks.id, recordData.TASK_ID))
      .limit(1)

    if (taskExists.length === 0) {
      throw new Error(
        `Task ${recordData.TASK_ID} not found for timerecord ${recordData.ID}. Import failed.`
      )
    }

    const startTimestamp = recordData.STARTTIME || recordData.startTimestamp
    const endTimestamp = recordData.ENDTIME || recordData.endTimestamp

    invariant(
      startTimestamp,
      `Start time not found for timerecord ${recordData.ID}, value ${startTimestamp}. Import failed.`
    )
    const parsedStart = parseDate(startTimestamp)
    invariant(
      parsedStart,
      `Invalid start time for timerecord ${recordData.ID}, value: ${startTimestamp}. Import failed.`
    )

    // Handle both old and new format fields
    const newTimerecord: NewTimerecord = {
      id: recordData.ID,
      taskId: recordData.TASK_ID,
      startTimestamp: parsedStart,
      endTimestamp: parseDate(endTimestamp),
      comment: recordData.COMMENT || recordData.comment,
      // Handle upload data in new format
      uploadData: recordData.uploadData,
      uploadSuccess:
        recordData.UPLOAD_SUCCESS || recordData.uploadSuccess || false,
      uploaded: parseDate(recordData.UPLOADED || recordData.uploaded),
      createdAt:
        parseDate(recordData.CREATED || recordData.createdAt) || new Date(),
      updatedAt:
        parseDate(recordData.MODIFIED || recordData.updatedAt) || new Date(),
      createdBy: recordData.createdBy || DEFAULT_USER_ID,
      updatedBy: recordData.updatedBy || DEFAULT_USER_ID,
    }

    await db.insert(timerecords).values(newTimerecord).onConflictDoUpdate({
      target: timerecords.id,
      set: newTimerecord,
    })

    // If there's a remoteIssueId in the old format, create a link in the junction table
    if (recordData.REMOTEISSUE_ID) {
      // Find the taskCatalog with this remoteIssueId
      const taskCatalogResult = await db
        .select({ id: taskCatalogs.id })
        .from(taskCatalogs)
        .where(eq(taskCatalogs.remoteId, recordData.REMOTEISSUE_ID))
        .limit(1)

      if (taskCatalogResult.length > 0) {
        const taskCatalogId = taskCatalogResult[0].id

        // Create the junction table entry
        const newTimerecordToTaskCatalog: NewTimerecordToTaskCatalog = {
          timerecordId: recordData.ID,
          taskCatalogId: taskCatalogId,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: DEFAULT_USER_ID,
          updatedBy: DEFAULT_USER_ID,
        }

        await db
          .insert(timerecordToTaskCatalogs)
          .values(newTimerecordToTaskCatalog)
          .onConflictDoUpdate({
            target: [
              timerecordToTaskCatalogs.timerecordId,
              timerecordToTaskCatalogs.taskCatalogId,
            ],
            set: newTimerecordToTaskCatalog,
          })
      } else {
        console.warn(
          `Warning: TaskCatalog with remoteId ${recordData.REMOTEISSUE_ID} not found for timerecord ${recordData.ID}`
        )
      }
    }

    importedCount++
  }

  console.log(
    `Imported ${importedCount} of ${timerecordsData.length} timerecords.`
  )
}

// Import timerecord to task catalog relationships
async function importTimerecordToTaskCatalogs(filePath: string): Promise<void> {
  console.log('Importing timerecord to task catalog relationships...')
  const relationshipsData = readJsonFile(filePath)
  let importedCount = 0

  for (const relationData of relationshipsData) {
    // Check if the timerecord exists
    const timerecordExists = await db
      .select({ id: timerecords.id })
      .from(timerecords)
      .where(
        eq(
          timerecords.id,
          relationData.timerecordId || relationData.TIMERECORD_ID
        )
      )
      .limit(1)

    if (timerecordExists.length === 0) {
      console.warn(
        `Warning: Timerecord ${relationData.timerecordId || relationData.TIMERECORD_ID} not found for relationship. Skipping.`
      )
      continue
    }

    // Check if the task catalog exists
    const taskCatalogExists = await db
      .select({ id: taskCatalogs.id })
      .from(taskCatalogs)
      .where(
        eq(
          taskCatalogs.id,
          relationData.taskCatalogId || relationData.TASK_CATALOG_ID
        )
      )
      .limit(1)

    if (taskCatalogExists.length === 0) {
      console.warn(
        `Warning: Task catalog ${relationData.taskCatalogId || relationData.TASK_CATALOG_ID} not found for relationship. Skipping.`
      )
      continue
    }

    // Create the junction table entry
    const newTimerecordToTaskCatalog: NewTimerecordToTaskCatalog = {
      timerecordId: relationData.timerecordId || relationData.TIMERECORD_ID,
      taskCatalogId: relationData.taskCatalogId || relationData.TASK_CATALOG_ID,
      createdAt:
        parseDate(relationData.createdAt || relationData.CREATED) || new Date(),
      updatedAt:
        parseDate(relationData.updatedAt || relationData.MODIFIED) ||
        new Date(),
      createdBy:
        relationData.createdBy || relationData.CREATED_BY || DEFAULT_USER_ID,
      updatedBy:
        relationData.updatedBy || relationData.UPDATED_BY || DEFAULT_USER_ID,
    }

    await db
      .insert(timerecordToTaskCatalogs)
      .values(newTimerecordToTaskCatalog)
      .onConflictDoUpdate({
        target: [
          timerecordToTaskCatalogs.timerecordId,
          timerecordToTaskCatalogs.taskCatalogId,
        ],
        set: newTimerecordToTaskCatalog,
      })

    importedCount++
  }

  console.log(
    `Imported ${importedCount} of ${relationshipsData.length} timerecord to task catalog relationships.`
  )
}

// Import queries
async function importQueries(filePath: string): Promise<void> {
  console.log('Importing queries...')
  const queriesData = readJsonFile(filePath)
  let importedCount = 0

  // We already checked that the table exists in checkDatabaseSchema()

  for (const queryData of queriesData) {
    const newQuery: NewQuery = {
      id: queryData.ID,
      name: queryData.NAME,
      position: queryData.POSITION ? queryData.POSITION.toString() : null,
      predicate: queryData.PREDICATE,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(queries).values(newQuery).onConflictDoUpdate({
      target: queries.id,
      set: newQuery,
    })
    importedCount++
  }

  console.log(`Imported ${importedCount} of ${queriesData.length} queries.`)
}

// Import timers
async function importTimers(filePath: string): Promise<void> {
  console.log('Importing timers...')
  const timersData = readJsonFile(filePath)
  let importedCount = 0

  // We already checked that the table exists in checkDatabaseSchema()

  for (const timerData of timersData) {
    // Check if the user exists
    const userExists = await db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.id, timerData.USER_ID))
      .limit(1)

    if (userExists.length === 0) {
      throw new Error(
        `User ${timerData.USER_ID} not found for timer ${timerData.ID}. Import failed.`
      )
    }

    const newTimer = {
      id: timerData.ID,
      userId: timerData.USER_ID,
      worklogId: timerData.TIMERECORD_ID ?? null,
      startTimestamp: parseDate(timerData.START_TIMESTAMP) || new Date(),
      endTimestamp: parseDate(timerData.END_TIMESTAMP),
      status: timerData.STATUS || 'stopped',
      createdAt: parseDate(timerData.CREATED) || new Date(),
      updatedAt: parseDate(timerData.MODIFIED) || new Date(),
      createdBy: DEFAULT_USER_ID,
      updatedBy: DEFAULT_USER_ID,
    }

    await db.insert(timers).values(newTimer).onConflictDoUpdate({
      target: timers.id,
      set: newTimer,
    })
    importedCount++
  }

  console.log(`Imported ${importedCount} of ${timersData.length} timers.`)
}

// Check if all required tables exist
async function checkDatabaseSchema(): Promise<void> {
  console.log('Checking database schema...')

  const requiredTables = [
    { name: 'user', schema: users },
    { name: 'customer', schema: customers },
    { name: 'remote_service', schema: remoteServices },
    { name: 'project_catalog', schema: projectCatalogs },
    { name: 'task_catalog', schema: taskCatalogs },
    { name: 'project', schema: projects },
    { name: 'task', schema: tasks },
    { name: 'task_to_task_catalogs', schema: taskToTaskCatalogs },
    { name: 'timerecord', schema: timerecords },
    { name: 'timerecord_to_task_catalogs', schema: timerecordToTaskCatalogs },
    { name: 'query', schema: queries },
    { name: 'timer', schema: timers },
  ]

  for (const table of requiredTables) {
    await db.select().from(table.schema).limit(1)
    console.log(`✓ Table '${table.name}' exists`)
  }

  console.log('Database schema check completed successfully.')
}

// Main import function
async function importAllData(dataDir: string): Promise<void> {
  console.log(`Starting import from ${dataDir}...`)

  // Check if all required tables exist before starting the import
  await checkDatabaseSchema()

  // Import in the correct order to handle dependencies
  await importUsers()

  // Map of file names to import functions
  const importFunctions: Record<string, (filePath: string) => Promise<void>> = {
    'TTNG_CUSTOMER.json': importCustomers,
    'TTNG_REMOTESERVICE.json': importRemoteServices,
    'TTNG_PROJECTCATALOG.json': importProjectCatalogs,
    'TTNG_TASKCATALOG.json': importTaskCatalogs,
    'TTNG_PROJECT.json': importProjects,
    'TTNG_TASK.json': importTasks,
    'TTNG_TIMERECORD.json': importTimerecords,
    'TTNG_TIMERECORD_TO_TASKCATALOG.json': importTimerecordToTaskCatalogs,
    'TTNG_QUERY.json': importQueries,
    'TTNG_TIMER.json': importTimers,
  }

  // Get all JSON files
  const jsonFiles = getJsonFiles(dataDir)
  if (jsonFiles.length === 0) {
    throw new Error(`No JSON files found in ${dataDir}. Please check the path.`)
  }

  // Define the order of imports to handle dependencies
  const importOrder = [
    'TTNG_CUSTOMER.json',
    'TTNG_REMOTESERVICE.json',
    'TTNG_PROJECTCATALOG.json',
    'TTNG_TASKCATALOG.json',
    'TTNG_PROJECT.json',
    'TTNG_TASK.json',
    'TTNG_TIMERECORD.json',
    'TTNG_TIMERECORD_TO_TASKCATALOG.json',
    'TTNG_QUERY.json',
    'TTNG_TIMER.json',
  ]

  // Create a map of file names to file paths
  const filePathMap: Record<string, string> = {}
  for (const filePath of jsonFiles) {
    const fileName = path.basename(filePath)
    filePathMap[fileName] = filePath
  }

  // Process files in the defined order
  for (const fileName of importOrder) {
    const filePath = filePathMap[fileName]
    const importFunction = importFunctions[fileName]

    if (filePath && importFunction) {
      console.log(`Processing ${fileName}...`)
      await importFunction(filePath)
    } else if (!filePath) {
      console.log(`Skipping ${fileName} - file not found.`)
    } else {
      console.log(`Skipping ${fileName} - no import function defined.`)
    }
  }

  console.log('Import completed successfully!')
}

// Run the import
// Look for data directory in project root (two levels up from current directory)
const dataDir = process.argv[2] || path.join(process.cwd(), '../../data')
importAllData(dataDir)
  .then(() => {
    console.log('All data imported successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Error importing data:', error)
    process.exit(1)
  })
