import fs from 'node:fs'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the fs and db modules
vi.mock('node:fs')
vi.mock('../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    execute: vi.fn().mockResolvedValue([]),
  },
}))

describe('Export JSON Script', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock fs.existsSync to return false so ensureDirectoryExists creates the directory
    vi.mocked(fs.existsSync).mockReturnValue(false)

    // Mock fs.mkdirSync to do nothing
    vi.mocked(fs.mkdirSync).mockImplementation(() => undefined)

    // Mock fs.writeFileSync to do nothing
    vi.mocked(fs.writeFileSync).mockImplementation(() => undefined)
  })

  it('should create the output directory if it does not exist', async () => {
    const { exportAllData } = await import('./export-json')
    const outputDir = '/test/export/path'

    await exportAllData(outputDir)

    expect(fs.existsSync).toHaveBeenCalledWith(outputDir)
    expect(fs.mkdirSync).toHaveBeenCalledWith(outputDir, { recursive: true })
  })

  it('should not create the output directory if it already exists', async () => {
    const { exportAllData } = await import('./export-json')
    const outputDir = '/test/export/path'

    // Mock fs.existsSync to return true
    vi.mocked(fs.existsSync).mockReturnValue(true)

    await exportAllData(outputDir)

    expect(fs.existsSync).toHaveBeenCalledWith(outputDir)
    expect(fs.mkdirSync).not.toHaveBeenCalled()
  })

  it('should write data to JSON files', async () => {
    const { exportAllData } = await import('./export-json')
    const outputDir = '/test/export/path'

    // Mock db.execute to return some test data
    const { db } = await import('../db')
    const testData = [{ id: '1', name: 'Test' }]
    vi.mocked(db.execute).mockResolvedValue({
      rows: testData,
      command: '',
      rowCount: testData.length,
      oid: 0,
      fields: [],
    })

    await exportAllData(outputDir)

    // Check that writeFileSync was called for each entity type
    expect(fs.writeFileSync).toHaveBeenCalled()

    // Check the format of one of the calls
    const writeFileSyncCalls = vi.mocked(fs.writeFileSync).mock.calls
    const hasUserJsonCall = writeFileSyncCalls.some((call) => {
      const filePath = call[0] as string
      return filePath.includes('TTNG_USER.json')
    })

    expect(hasUserJsonCall).toBe(true)
  })
})
