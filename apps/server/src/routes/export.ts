import path from 'node:path'
import { zValidator } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'
import { exportAllData } from '../scripts/export-json'

export const exportRouter = new Hono()

// Schema for export request
const exportRequestSchema = z.object({
  outputDir: z.string().optional(),
})

// POST endpoint to trigger export
exportRouter.post('/', zValidator('json', exportRequestSchema), async (c) => {
  try {
    const { outputDir } = c.req.valid('json')

    // Default to data/export directory if not specified
    const exportPath = outputDir || path.join(process.cwd(), 'data/export')

    // Run the export asynchronously
    await exportAllData(exportPath)

    return c.json({
      success: true,
      message: 'Database exported successfully',
      outputDir: exportPath,
    })
  } catch (error) {
    console.error('Export failed:', error)
    return c.json(
      {
        success: false,
        message: 'Export failed',
        error: error instanceof Error ? error.message : String(error),
      },
      500
    )
  }
})
