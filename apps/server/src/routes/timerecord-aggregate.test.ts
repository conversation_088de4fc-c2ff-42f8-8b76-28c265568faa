import { beforeEach, describe, expect, it, vi } from 'vitest'
import { timerecordAggregateRouter } from './timerecord-aggregate'

// Mock the database module
vi.mock('~/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    execute: vi.fn().mockResolvedValue([
      {
        customerId: '123',
        customerName: 'Test Customer',
        projectId: '456',
        projectName: 'Test Project',
        taskId: '789',
        taskName: 'Test Task',
        totalDuration: 3600000, // 1 hour in milliseconds
      },
    ]),
  },
}))

// Mock the drizzle schema
vi.mock('../../drizzle/drizzle-schema', () => ({
  customers: { id: {}, name: {} },
  projects: { id: {}, name: {}, customerId: {} },
  tasks: { id: {}, name: {}, projectId: {} },
  timerecords: {
    id: {},
    taskId: {},
    startTimestamp: {},
    endTimestamp: {},
    createdBy: {},
  },
}))

// Mock the auth middleware
vi.mock('~/middleware/auth', () => ({
  jwtAuth: vi.fn().mockImplementation((c, next) => {
    // Mock the auth context
    c.set('auth', { userId: 'test-user-id' })
    return next()
  }),
}))

describe.skip('Timerecord Aggregate Router', () => {
  beforeEach(() => {
    // Clear mock calls between tests
    vi.clearAllMocks()
  })

  it('should aggregate timerecords based on provided filters', async () => {
    const res = await timerecordAggregateRouter.request('/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customerIds: ['123'],
        projectIds: ['456'],
        taskIds: ['789'],
        startTimestamp: 1609459200000, // 2021-01-01
        endTimestamp: 1640995200000, // 2022-01-01
      }),
    })

    const data = await res.json()
    expect(res.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.data).toEqual([
      {
        customerId: '123',
        customerName: 'Test Customer',
        projectId: '456',
        projectName: 'Test Project',
        taskId: '789',
        taskName: 'Test Task',
        totalDuration: 3600000,
      },
    ])
  })

  it('should handle validation errors', async () => {
    const res = await timerecordAggregateRouter.request(
      '/timerecord-aggregate',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Missing required startTimestamp and endTimestamp
          customerIds: ['123'],
        }),
      }
    )

    expect(res.status).toBe(400) // Bad request due to validation error
  })

  it('should handle server errors', async () => {
    // Mock the database to throw an error
    const db = await import('~/db')
    vi.mocked(db.db.execute).mockRejectedValueOnce(new Error('Database error'))

    const res = await timerecordAggregateRouter.request(
      '/timerecord-aggregate',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startTimestamp: 1609459200000,
          endTimestamp: 1640995200000,
        }),
      }
    )

    expect(res.status).toBe(500)
    const data = await res.json()
    expect(data.success).toBe(false)
    expect(data.message).toBe('Timerecord aggregation failed')
  })
})
