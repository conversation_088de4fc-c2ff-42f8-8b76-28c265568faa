import { eq } from 'drizzle-orm'
import { Hono } from 'hono'
import { db } from '~/db'
import { jwtAuth } from '~/middleware/auth'
import { users } from '../../drizzle/drizzle-schema'

export const usersRouter = new Hono()

// Apply JWT authentication middleware to all routes
usersRouter.use('*', jwtAuth)

// GET /api/users/me - Get current user information
usersRouter.get('/me', async (c) => {
  try {
    // Get the authenticated user ID from the context
    const { userId } = c.get('auth')

    // Fetch user from database
    const user = await db
      .select({
        id: users.id,
        name: users.name,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1)

    if (user.length === 0) {
      return c.json({ success: false, message: 'User not found' }, 404)
    }

    return c.json({
      success: true,
      data: user[0],
    })
  } catch (error) {
    console.error('Error fetching user:', error)
    return c.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error),
      },
      500
    )
  }
})
