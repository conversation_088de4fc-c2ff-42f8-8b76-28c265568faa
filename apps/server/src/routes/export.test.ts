import { beforeEach, describe, expect, it, vi } from 'vitest'
import { exportRouter } from './export'

// Mock the export-json module
vi.mock('../scripts/export-json', () => ({
  exportAllData: vi.fn().mockResolvedValue(undefined),
}))

describe.skip('Export Router', () => {
  beforeEach(() => {
    // Clear mock calls between tests
    vi.clearAllMocks()
  })

  it('should export data to the default directory when no outputDir is provided', async () => {
    const { exportAllData } = await import('../scripts/export-json')

    const res = await exportRouter.request('/api/export/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    })

    expect(res.status).toBe(200)
    const data = await res.json()
    expect(data.success).toBe(true)
    expect(data.message).toBe('Database exported successfully')
    expect(data.outputDir).toContain('data/export')

    expect(exportAllData).toHaveBeenCalledTimes(1)
    expect(exportAllData).toHaveBeenCalledWith(
      expect.stringContaining('data/export')
    )
  })

  it('should export data to the specified directory', async () => {
    const { exportAllData } = await import('../scripts/export-json')

    const customDir = '/tmp'
    const res = await exportRouter.request('/api/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ outputDir: customDir }),
    })

    expect(res.status).toBe(200)
    const data = await res.json()
    expect(data.success).toBe(true)
    expect(data.outputDir).toBe(customDir)

    expect(exportAllData).toHaveBeenCalledTimes(1)
    expect(exportAllData).toHaveBeenCalledWith(customDir)
  })

  it('should handle export errors', async () => {
    const { exportAllData } = await import('../scripts/export-json')
    const errorMessage = 'Export failed for some reason'

    // Mock the export function to throw an error
    vi.mocked(exportAllData).mockRejectedValueOnce(new Error(errorMessage))

    const res = await exportRouter.request('/api/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    })

    expect(res.status).toBe(500)
    const data = await res.json()
    expect(data.success).toBe(false)
    expect(data.message).toBe('Export failed')
    expect(data.error).toBe(errorMessage)
  })
})
