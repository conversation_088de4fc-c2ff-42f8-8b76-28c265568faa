import { describe, expect, it, vi } from 'vitest'
import { app } from './index'
import { jwtAuth } from './middleware/auth'

// Mock the database module for the timerecord-aggregate endpoint
vi.mock('~/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    execute: vi.fn().mockResolvedValue([]),
  },
}))

// Mock the drizzle schema
vi.mock('./drizzle/drizzle-schema', () => ({
  customers: { id: {}, name: {} },
  projects: { id: {}, name: {}, customerId: {} },
  tasks: { id: {}, name: {}, projectId: {} },
  timerecords: {
    id: {},
    taskId: {},
    startTimestamp: {},
    endTimestamp: {},
    createdBy: {},
  },
}))

// Mock the auth middleware
vi.mock('./middleware/auth', () => ({
  jwtAuth: vi.fn().mockImplementation((c, next) => {
    // Mock the auth context
    c.set('auth', { userId: 'test-user-id' })
    return next()
  }),
}))

const jwtAuthMock = vi.mocked(jwtAuth)

describe('API Endpoints', () => {
  describe('Health Check', () => {
    it('should return status ok', async () => {
      const res = await app.request('/api/health')
      expect(res.status).toBe(200)

      const data = await res.json()
      expect(data).toEqual({ status: 'ok' })
    })
  })

  describe('Root Endpoint', () => {
    it('should return the API name', async () => {
      const res = await app.request('/api/')
      expect(res.status).toBe(200)

      const text = await res.text()
      expect(text).toBe('FocusTimeTracker API')
    })
  })

  describe('Timerecord Aggregate Endpoint', () => {
    it('should be registered and accept POST requests with JWT token', async () => {
      const res = await app.request('/api/timerecord-aggregate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer test-jwt-token',
        },
        body: JSON.stringify({
          startTimestamp: 1609459200000, // 2021-01-01
          endTimestamp: 1640995200000, // 2022-01-01
        }),
      })

      // We're just checking that the route is registered and responds
      expect(res.status).toBe(200)
    })

    it('should return 401 when no JWT token is provided', async () => {
      // Mock the auth middleware to fail when no token is provided
      // @ts-ignore: dont care
      jwtAuthMock.mockImplementationOnce((c) => {
        return c.json(
          { success: false, message: 'Authentication required' },
          401
        )
      })

      const res = await app.request('/api/timerecord-aggregate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startTimestamp: 1609459200000, // 2021-01-01
          endTimestamp: 1640995200000, // 2022-01-01
        }),
      })

      expect(res.status).toBe(401)
      const data = await res.json()
      expect(data.success).toBe(false)
      expect(data.message).toBe('Authentication required')
    })
  })
})
