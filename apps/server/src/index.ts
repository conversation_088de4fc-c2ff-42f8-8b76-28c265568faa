import { serve } from '@hono/node-server'
import { migrate } from 'drizzle-orm/node-postgres/migrator'
import { invariant } from 'es-toolkit'
import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'

import { setCookie } from 'hono/cookie'
import { SignJWT } from 'jose'
import { db } from '~/db'
import { env } from '~/env' // On server
import { timerecordAggregateRouter } from './routes/timerecord-aggregate'
import { usersRouter } from './routes/users'

export const config = {
  runtime: 'edge',
}

export const app = new Hono().basePath('/api')

// Add default export for Vite build
export default app

const authSecret = env.ZERO_AUTH_SECRET
invariant(authSecret, 'no auth secret')

console.log('using auth secret', authSecret)

// See seed.sql
// In real life you would of course authenticate the user however you like.
const userIDs = ['c50b8068-23d8-4686-8f0b-e22bd19a5ecd']

// Add CORS middleware
app.use(
  '*',
  cors({
    origin: ['http://localhost:1420'],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
    maxAge: 600,
    credentials: true,
  })
)

app.use(logger())
app.get('/login', async (c) => {
  const jwtPayload = {
    sub: userIDs[0],
    iat: Math.floor(Date.now() / 1000),
  }

  const jwt = await new SignJWT(jwtPayload)
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('30days')
    .sign(new TextEncoder().encode(authSecret))

  setCookie(c, 'jwt', jwt, {
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    path: '/',
    httpOnly: false, // Allow JavaScript access for Tauri app
    secure: false, // Allow non-HTTPS in development
    sameSite: 'none', // Allow cross-site cookies
  })

  return c.json({ status: 'ok', jwt })
})

app.get('', (c) => c.text('FocusTimeTracker API'))
app.get('/health', (c) => c.json({ status: 'ok' }))

// Register the timerecord-aggregate router
app.route('/timerecord-aggregate', timerecordAggregateRouter)

// Register the users router
app.route('/users', usersRouter)

async function startup() {
  console.log('migrating tables')
  await migrate(db, {
    migrationsFolder: './drizzle',
    migrationsTable: 'drizzle_migrations',
    migrationsSchema: 'public',
  })
  console.log('migrated tables, starting server')

  serve(
    {
      fetch: app.fetch,
      port: env.PORT,
    },
    (info) => {
      console.log(`Server is running on http://localhost:${info.port}`)
    }
  )
}

startup()
  .then(() => console.log('startup done'))
  .catch((e) => console.error('startup failed', e))
