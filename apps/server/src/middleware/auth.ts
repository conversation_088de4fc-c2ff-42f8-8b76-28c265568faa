import type { Context, MiddlewareHandler, Next } from 'hono'
import { getCookie } from 'hono/cookie'
import { jwtVerify } from 'jose'
import { env } from '~/env'

export interface AuthInfo {
  userId: string
}

declare module 'hono' {
  interface ContextVariableMap {
    auth: AuthInfo
  }
}

/**
 * Middleware to verify JWT token and extract user ID
 *
 * This middleware checks for a JWT token in the following order:
 * 1. Authorization header (Bearer token)
 * 2. <PERSON><PERSON> named 'jwt'
 *
 * If a valid token is found, it adds the user ID to the context
 * If no token is found or the token is invalid, it returns a 401 Unauthorized response
 */
export const jwtAuth: MiddlewareHandler = async (c: Context, next: Next) => {
  // Get the JWT token from the Authorization header or cookie
  const authHeader = c.req.header('Authorization')
  let token: string | undefined

  if (authHeader?.startsWith('Bearer ')) {
    token = authHeader.substring(7)
  } else {
    // Try to get the token from the cookie
    token = getCookie(c, 'jwt')
  }

  if (!token) {
    return c.json({ success: false, message: 'Authentication required' }, 401)
  }

  try {
    // Verify the token
    const authSecret = env.ZERO_AUTH_SECRET
    const verified = await jwtVerify(
      token,
      new TextEncoder().encode(authSecret)
    )

    // Extract the user ID from the token
    const userId = verified.payload.sub
    if (!userId) {
      return c.json(
        { success: false, message: 'Invalid token: missing user ID' },
        401
      )
    }

    // Add the user ID to the context
    c.set('auth', { userId })

    // Continue to the next middleware or handler
    await next()
  } catch (error) {
    console.error('JWT verification failed:', error)
    return c.json({ success: false, message: 'Invalid or expired token' }, 401)
  }
}
