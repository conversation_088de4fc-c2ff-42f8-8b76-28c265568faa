import tsconfigPaths from 'vite-tsconfig-paths'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  plugins: [tsconfigPaths()],
  test: {
    environment: 'node',
    include: ['**/*.test.{ts,js}'],
    exclude: ['node_modules', 'dist'],
    setupFiles: ['./vitest.setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'dist/'],
    },
    env: {
      NODE_ENV: 'test',
      ZERO_AUTH_SECRET: 'test-secret',
      DATABASE_URL: 'postgresql://user:password@localhost:5432/testdb',
      PORT: '3030',
    },
  },
})
