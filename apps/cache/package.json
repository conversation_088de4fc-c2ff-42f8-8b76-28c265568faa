{"name": "@ftt/cache", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "pnpm dev:zero-cache", "dev:zero-cache": "zero-cache-dev -p ../../packages/shared/src/schema.ts", "lint": "biome lint .", "format": "biome format --write .", "format:check": "biome format ."}, "dependencies": {"@ftt/shared": "workspace:*", "@rocicorp/zero": "catalog:", "jose": "^6.0.10", "js-cookie": "^3.0.5", "uuidv7": "^1.0.2", "vite-tsconfig-paths": "^5.1.4"}, "devDependencies": {"@rocicorp/zero-sqlite3": "catalog:", "typescript": "~5.8.3", "vite": "^6.3.2"}, "trustedDependencies": ["@rocicorp/zero-sqlite3"], "overrides": {"esbuild": "0.25.0"}, "volta": {"node": "22.15.0"}}