// This file is used to setup the test environment
// It will be run before each test file
import { afterEach, beforeEach, vi } from 'vitest'

// Set up timezone mocking for consistent test behavior
beforeEach(() => {
  // Store the original timezone methods
  const originalDateTimeFormat = Intl.DateTimeFormat

  // Mock Intl.DateTimeFormat to always use UTC
  vi.stubGlobal('Intl', {
    ...Intl,
    DateTimeFormat: function DateTimeFormatMock(locale, options) {
      return new originalDateTimeFormat(locale, { ...options, timeZone: 'UTC' })
    },
  })
})

afterEach(() => {
  // Restore all mocks
  vi.restoreAllMocks()
})

// You can add global mocks or setup code here
export default function setup() {
  // Add any setup code here
}
