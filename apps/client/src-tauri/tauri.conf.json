{"$schema": "https://schema.tauri.app/config/2", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "productName": "timetracker-<PERSON><PERSON>", "mainBinaryName": "timetracker-<PERSON><PERSON>", "version": "0.1.0", "identifier": "app.focustimetracker.tauri", "plugins": {}, "app": {"security": {"csp": null}, "windows": [{"title": "timetracker-<PERSON><PERSON>", "width": 800, "height": 600, "useHttpsScheme": true}]}}