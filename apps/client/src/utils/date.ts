/**
 * Format a date to YYYY-MM-DD
 */
export function formatDate(date: Date, timezone?: string): string {
  const format = timezone
    ? new Intl.DateTimeFormat('de-DE', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: timezone,
      })
    : dateFormat
  return format.format(date)
}

const timeFormat = new Intl.DateTimeFormat('de-DE', {
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
})

const dateFormat = new Intl.DateTimeFormat('de-DE', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
})

/**
 * Format a time to HH:MM:SS
 */
export function formatTime(date: Date, timezone?: string): string {
  // format using intl
  const format = timezone
    ? new Intl.DateTimeFormat('de-DE', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: timezone,
      })
    : timeFormat

  return format.format(date)
}

/**
 * Format a date and time to YYYY-MM-DD HH:MM:SS
 */
export function formatDateTime(date: Date, timezone?: string): string {
  return `${formatDate(date, timezone)} ${formatTime(date, timezone)}`
}

export function formatDateTimeFromTimestamp(
  timestamp: number,
  timezone?: string
): string {
  return formatDateTime(new Date(timestamp), timezone)
}

export function formatOptDateTimeFromTimestamp(
  timestamp: number | undefined,
  timezone?: string
): string {
  return timestamp ? formatDateTimeFromTimestamp(timestamp, timezone) : '--'
}
