import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { formatDate, formatDateTime, formatTime } from './date'

describe('Date utilities', () => {
  beforeEach(() => {
    // Mock the Date object to always return a fixed date in UTC
    const mockDate = new Date('2023-05-15T12:30:45Z')
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
  })

  it('should format date correctly', () => {
    // Create a new date that will use our mocked system time
    const date = new Date('2023-05-15T12:30:45Z')
    expect(formatDate(date, 'UTC')).toBe('15.05.2023')
  })

  it('should format time correctly', () => {
    // Create a date with explicit time components
    const date = new Date('2023-05-15T12:30:45Z')

    // Since we're using toISOString which always returns UTC time,
    // we can directly check the expected format
    expect(formatTime(date, 'UTC')).toBe('12:30:45')
  })

  it('should format date and time correctly', () => {
    // Create a date with explicit time components
    const date = new Date('2023-05-15T12:30:45Z')

    // Since we're using toISOString which always returns UTC time,
    // we can directly check the expected format
    expect(formatDateTime(date, 'UTC')).toBe('15.05.2023 12:30:45')
  })

  afterEach(() => {
    // Restore the Date object
    vi.useRealTimers()
  })
})
