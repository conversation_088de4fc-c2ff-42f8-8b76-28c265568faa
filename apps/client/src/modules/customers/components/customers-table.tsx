import type { Customer, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import type { ColumnDef } from '@tanstack/react-table'
import { type FC, useMemo, useRef } from 'react'
import { DataTable, type DataTableRef } from '~/components/datatable'
import type { TimeRecord } from '~/components/time-record-editor'

import { sortBy } from 'es-toolkit'
import { formatDurationFromMs } from '~/lib/utils/date-time'
import { formatPrice } from '~/lib/utils/formatPrice'
import style from './customers-table.module.css'

interface CustomersTableProps {
  selectedCustomerId: string | undefined
  onCustomerChange: (customerId: string | undefined) => void
  runningTimeRecordId?: string
  durationOfRunningTimeRecord?: number
  timerecords: TimeRecord[]
}

export const CustomersTable: FC<CustomersTableProps> = ({
  selectedCustomerId,
  onCustomerChange,
  timerecords,
  runningTimeRecordId,
  durationOfRunningTimeRecord,
}) => {
  const z = useZero<Schema>()
  const tableRef = useRef<DataTableRef>(null)

  const [customers = []] = useQuery(z.query.customers.limit(100), {
    ttl: 'forever',
  })

  // Calculate duration by customer
  const durationByCustomer = useMemo(() => {
    const resultMap = new Map<string, number>()
    for (const record of timerecords) {
      const customerId = record.task?.project?.customerId
      if (customerId) {
        const currentDuration = resultMap.get(customerId) ?? 0
        const duration = calculateRecordDuration(
          record,
          runningTimeRecordId,
          durationOfRunningTimeRecord
        )
        resultMap.set(customerId, currentDuration + duration)
      }
    }
    return resultMap
  }, [timerecords, durationOfRunningTimeRecord, runningTimeRecordId])

  // Sort customers by duration
  const sortedCustomers = useMemo(() => {
    return sortBy(customers, [
      (customer) => durationByCustomer.get(customer.id) ?? 0,
    ]).toReversed()
  }, [customers, durationByCustomer])

  // Find customer ID of running timerecord
  const customerIdOfRunningTimerecord = useMemo(() => {
    const timeRecord = timerecords.find(
      (record) => record.id === runningTimeRecordId
    )
    return timeRecord?.task?.project?.customerId
  }, [timerecords, runningTimeRecordId])

  // Define columns for the DataTable
  const columns = useMemo<ColumnDef<Customer>[]>(() => {
    return [
      {
        accessorKey: 'name',
        header: 'Customer',
        cell: ({ row }) => row.original.name,
      },
      {
        accessorKey: 'rateValue',
        header: 'Rate',
        cell: ({ row }) => row.original.rateValue,
      },
      {
        accessorKey: 'duration',
        header: 'Duration',
        cell: ({ row }) => {
          const duration = durationByCustomer.get(row.original.id) ?? 0
          return formatDurationFromMs(duration)
        },
      },
      {
        accessorKey: 'price',
        header: 'Price',
        cell: ({ row }) => {
          const duration = durationByCustomer.get(row.original.id) ?? 0
          const durationInHours = duration / 1000 / 60 / 60
          const price = durationInHours * (row.original.rateValue ?? 0)
          return formatPrice(price)
        },
      },
    ]
  }, [durationByCustomer])

  // Handle row click
  const handleRowClick = (customer: Customer) => {
    onCustomerChange(customer.id)
  }

  // Highlight running timerecord's customer
  const highlightedRowId = customerIdOfRunningTimerecord

  return (
    <div className={`${style.customersTable} mb-2`}>
      <DataTable
        ref={tableRef}
        columns={columns}
        data={sortedCustomers}
        onRowClick={handleRowClick}
        getRowId={(customer: Customer) => customer.id}
        selectedRowId={selectedCustomerId}
        highlightedRowId={highlightedRowId}
        stickyHeaders={true}
      />
    </div>
  )
}

function calculateRecordDuration(
  record: TimeRecord,
  runningTimeRecordId: string | undefined,
  durationOfRunningTimeRecord: number | undefined
) {
  if (
    record.id === runningTimeRecordId &&
    durationOfRunningTimeRecord !== undefined
  ) {
    return durationOfRunningTimeRecord
  }
  if (!record.endTimestamp) {
    return 0
  }
  return record.endTimestamp - record.startTimestamp
}
