import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import { uuidv7 } from 'uuidv7'
import { z } from 'zod'

// Define Zod schema for the JSON structure
export const TaskTypeSchema = z.object({
  name: z.string(),
  codeName: z.string(),
  effectsBalanceUpTo: z.string().nullable(),
  effectsBalance: z.boolean(),
  effectsBillingRate: z.boolean(),
  effectsBillingRateUpTo: z.string().nullable(),
  effectsReportedWorktime: z.boolean(),
  effectsReportedWorktimeUpTo: z.string().nullable(),
  editable: z.boolean(),
  allowNegative: z.boolean(),
  allowPositive: z.boolean(),
})

export const TimeUnitSchema = z.object({
  timeUnitId: z.number(),
  name: z.string(),
  codeName: z.string(),
  numberOfHours: z.number(),
  precision: z.number(),
})

export const ProjectResponsibleSchema = z.object({
  name: z.string(),
  email: z.string().email(),
})

export const SubsidiarySchema = z.object({
  id: z.number(),
  name: z.string(),
  extId: z.string(),
})

export const TaskSchema = z.object({
  projectTaskId: z.number(),
  projectId: z.number(),
  name: z.string(),
  commentMandatory: z.boolean(),
  taskStart: z.string().optional(),
  taskEnd: z.string().nullable(),
  taskType: TaskTypeSchema,
  timeUnit: TimeUnitSchema,
  disallowedRecordDates: z.array(z.string()).optional(),
  subTasks: z.array(z.unknown()).default([]),
  open: z.boolean().optional(),
  disabled: z.boolean().optional(),
})

export const ProjectSchema = z.object({
  projectId: z.number(),
  name: z.string(),
  projectExtId: z.string().optional(),
  projectResponsible: ProjectResponsibleSchema.optional(),
  parentProjectId: z.number().nullable().optional(),
  subsidiary: SubsidiarySchema.optional(),
  subProjects: z.array(z.unknown()).default([]),
  tasks: z.array(TaskSchema).default([]),
  internal: z.boolean().optional(),
  open: z.boolean().optional(),
  rootProject: z.boolean().optional(),
})

export const CustomerSchema = z.object({
  customerId: z.number(),
  name: z.string(),
  status: z.string(),
  externalId: z.string().nullish(),
  projects: z.array(ProjectSchema).default([]),
})

export const ImportJsonSchema = z.object({
  content: z.array(CustomerSchema),
})

// Type for the parsed JSON
export type ImportJson = z.infer<typeof ImportJsonSchema>

/**
 * Parses and imports project catalogs and task catalogs from the provided JSON
 */
export async function importProjectsAndTasks(
  z: Zero<Schema>,
  customerId: string,
  remoteServiceId: string,
  jsonData: ImportJson
): Promise<{
  projectCatalogsCreated: number
  projectCatalogsUpdated: number
  taskCatalogsCreated: number
  taskCatalogsUpdated: number
  errors: string[]
}> {
  const result = {
    projectCatalogsCreated: 0,
    projectCatalogsUpdated: 0,
    taskCatalogsCreated: 0,
    taskCatalogsUpdated: 0,
    errors: [] as string[],
  }

  try {
    // Process each customer in the JSON
    for (const customer of jsonData.content) {
      // Process each project for the customer
      for (const project of customer.projects) {
        try {
          const remoteId = project.projectId.toString()

          // Check if project catalog already exists with this remoteId
          const existingProject = await z.query.projectCatalogs
            .where('remoteId', '=', remoteId)
            .one()
            .run()

          let projectCatalogId: string

          if (existingProject) {
            // Update existing project catalog
            projectCatalogId = existingProject.id
            await z.mutate.projectCatalogs.update({
              id: existingProject.id,
              name: project.name,
              key: project.projectExtId || '',
              remoteId,
              remoteServiceId,
              updatedAt: Date.now(),
              updatedBy: z.userID,
            })
            result.projectCatalogsUpdated++
          } else {
            // Create new project catalog entry
            projectCatalogId = uuidv7()
            await z.mutate.projectCatalogs.insert({
              id: projectCatalogId,
              remoteServiceId,
              name: project.name,
              key: project.projectExtId || '',
              remoteId,
              remoteUrl: '',
              createdAt: Date.now(),
              updatedAt: Date.now(),
              createdBy: z.userID,
              updatedBy: z.userID,
            })
            result.projectCatalogsCreated++
          }
          // now check if we already have a local project for each remote project
          let localProject = await z.query.projects
            .where('projectCatalogId', '=', projectCatalogId)
            .one()
            .run()

          if (!localProject) {
            const newProjectId = uuidv7()
            await z.mutate.projects.insert({
              id: newProjectId,
              name: project.name,
              customerId,
              projectCatalogId,
              color: '',
              timeNormalizationType: '',
              timeNormalizationConfig: '',
              createdAt: Date.now(),
              updatedAt: Date.now(),
              createdBy: z.userID,
              updatedBy: z.userID,
            })
            localProject = await z.query.projects
              .where('id', '=', newProjectId)
              .one()
              .run()
          }

          invariant(localProject, 'Local project not found')

          // Process each task for the project
          for (const task of project.tasks) {
            try {
              const taskRemoteId = task.projectTaskId.toString()

              // Check if task catalog already exists with this remoteId
              const existingTask = await z.query.taskCatalogs
                .where('remoteId', '=', taskRemoteId)
                .one()
                .run()

              if (existingTask) {
                // Update existing task catalog
                await z.mutate.taskCatalogs.update({
                  id: existingTask.id,
                  name: task.name,
                  key: task.name,
                  status: task.disabled ? 'CLOSED' : 'OPEN',
                  remoteId: taskRemoteId,
                  projectCatalogId,
                  updatedAt: Date.now(),
                  updatedBy: z.userID,
                })
                result.taskCatalogsUpdated++
              } else {
                // Create new task catalog entry
                await z.mutate.taskCatalogs.insert({
                  id: uuidv7(),
                  name: task.name,
                  key: task.name,
                  status: task.disabled ? 'CLOSED' : 'OPEN',
                  remoteId: taskRemoteId,
                  remoteUrl: '',
                  projectCatalogId,
                  lastUsed: null,
                  pinned: false,
                  createdAt: Date.now(),
                  updatedAt: Date.now(),
                  createdBy: z.userID,
                  updatedBy: z.userID,
                })
                result.taskCatalogsCreated++
              }
            } catch (error) {
              result.errors.push(
                `Error processing task catalog for task ${task.name}: ${error}`
              )
            }
          }
        } catch (error) {
          result.errors.push(
            `Error processing project catalog for project ${project.name}: ${error}`
          )
        }
      }
    }
  } catch (error) {
    result.errors.push(`General import error: ${error}`)
  }
  return result
}
