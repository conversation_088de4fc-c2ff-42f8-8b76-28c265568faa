import type { RemoteService, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import type { ColumnDef } from '@tanstack/react-table'
import type { FC } from 'react'
import { DataTable } from '~/components/datatable'

const columns: ColumnDef<RemoteService>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
  },
  {
    accessorKey: 'name',
    header: 'Name',
  },
]

type RemoteServiceTableProps = {
  onRowClick?: (id: string) => void
  onRowDoubleClick?: (id: string) => void
}

export const RemoteServiceTable: FC<RemoteServiceTableProps> = ({
  onRowClick: _onRowClick,
  // onRowDoubleClick,
}) => {
  const z = useZero<Schema>()
  const [remoteServices = []] = useQuery(
    z.query.remoteServices.limit(100).orderBy('name', 'asc'),
    {
      ttl: 'forever',
    }
  )

  return (
    <div>
      <DataTable columns={columns} data={remoteServices} />
    </div>
  )
}
