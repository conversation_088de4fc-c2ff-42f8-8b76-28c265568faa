import React, { type FC, type <PERSON>ps<PERSON>ith<PERSON>hildren, useContext } from 'react'
import {
  type AuthContext as AuthHookContext,
  useAuth as useAuthHook,
} from '~/hooks/use-auth'

export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

export interface SAuthContext extends AuthHookContext {}

const AuthContext = React.createContext<SAuthContext | null>(null)

export const AuthProvider: FC<PropsWithChildren> = ({ children }) => {
  const auth = useAuthHook()

  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
