import { decodeJwt } from 'jose'
import Cookies from 'js-cookie'

export function getJwt() {
  // Log all cookies for debugging
  console.log('All cookies:', document.cookie)

  // Try to get JWT from cookie first, then localStorage as fallback
  let encodedJWT = Cookies.get('jwt')
  if (!encodedJWT) {
    // Try localStorage as fallback
    encodedJWT = localStorage.getItem('jwt') || undefined
    console.log('Using JWT from localStorage:', !!encodedJWT)

    // If we found it in localStorage but not in cookies, set the cookie
    if (encodedJWT) {
      Cookies.set('jwt', encodedJWT)
      console.log('Restored JWT cookie from localStorage')
    }
  } else {
    console.log('Using JWT from cookie')
  }
  return encodedJWT
}

export function getJwtPayload() {
  const encodedJWT = getJwt()
  console.log('JWT available:', !!encodedJWT)
  const decodedJWT = encodedJWT && decodeJwt(encodedJWT)
  const userID = decodedJWT?.sub ? (decodedJWT.sub as string) : 'anon'
  return { userID, encodedJWT }
}
