import { uuidv7 } from 'uuidv7'

const CLIENT_ID_KEY = 'client_id'

export const clientId = initializeClientId()

export function getActiveClientId() {
  return localStorage.getItem(CLIENT_ID_KEY)
}

export function setActiveClientId(clientId: string) {
  localStorage.setItem(CLIENT_ID_KEY, clientId)
}

function initializeClientId() {
  const clientId = getActiveClientId()
  if (!clientId) {
    const newClientId = uuidv7()
    setActiveClientId(newClientId)
    return newClientId
  }
  return clientId
}
