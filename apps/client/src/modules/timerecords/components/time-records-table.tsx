import {
  type RefObject,
  useCallback,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
} from 'react'
import { toast } from 'sonner'
import { DataTable, type DataTableRef } from '~/components/datatable'
import type { TimeRecord } from '~/components/time-record-editor'
import type { TimerStruct } from '~/lib/model/app-state'
import { formatDate, formatTime } from '~/lib/utils/date-time'
import { DayTimeGraph } from './day-time-graph'
import { createTimeRecordColumns } from './time-records-columns'

export interface TimeRecordsTableRef {
  focus: () => void
  getTableElement: () => HTMLDivElement | null
  copySelectedTimeRecord: () => void
}

interface TimeRecordsTableProps {
  timeRecords: TimeRecord[]
  selectedRowId?: string
  runningTimer?: TimerStruct
  currentTime: number
  onRowClick: (id: string) => void
  onRowDoubleClick: (id: string) => void
  selectedDate?: string
  onKeyDown?: (e: React.KeyboardEvent) => void
  ref?: RefObject<TimeRecordsTableRef | null>
  // ref?: { current: TimeRecordsTableRef | null }
}

/**
 * Table component for displaying time records
 */
export function TimeRecordsTable({
  timeRecords,
  selectedRowId,
  runningTimer,
  onRowClick,
  onRowDoubleClick,
  selectedDate,
  onKeyDown,
  currentTime,
  ref,
}: TimeRecordsTableProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const dataTableRef = useRef<DataTableRef>(null)

  // Function to copy the selected time record to clipboard
  const copySelectedTimeRecord = useCallback(() => {
    if (selectedRowId) {
      const record = timeRecords.find((record) => record.id === selectedRowId)
      if (record) {
        const date = formatDate(record.startTimestamp)
        const startTime = formatTime(record.startTimestamp)
        const endTime = record.endTimestamp
          ? formatTime(record.endTimestamp)
          : 'ongoing'
        const comment = record.comment || ''

        const formattedText = `${date} ${startTime}-${endTime} ${comment}`

        navigator.clipboard
          .writeText(formattedText)
          .then(() => {
            toast.success('Copied to clipboard', {
              description: formattedText,
              duration: 2000,
            })
          })
          .catch((err) => {
            console.error('Failed to copy text: ', err)
            toast.error('Failed to copy', {
              description: 'Could not copy to clipboard',
              duration: 2000,
            })
          })
      }
    }
  }, [selectedRowId, timeRecords])

  // Expose methods to parent component through ref
  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        dataTableRef.current?.focus()
      },
      getTableElement: () => containerRef.current,
      copySelectedTimeRecord,
    }),
    [copySelectedTimeRecord]
  )

  // Handle keyboard events
  function handleKeyDown(e: React.KeyboardEvent) {
    // Copy to clipboard with Ctrl+C or Cmd+C
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
      copySelectedTimeRecord()
      e.preventDefault()
      return
    }

    // Call the parent's onKeyDown handler if provided
    if (onKeyDown) {
      onKeyDown(e)
    }
  }

  // Create columns for the DataTable
  const columns = useMemo(
    () => createTimeRecordColumns(runningTimer, currentTime, 0),
    [runningTimer, currentTime]
  )

  // Get the ID of the running timer's time record
  const runningTimerTimerecordId = runningTimer?.worklogId

  // Handle row click (select)
  const handleRowClick = useCallback(
    (row: TimeRecord) => {
      onRowClick(row.id)
    },
    [onRowClick]
  )

  // Handle row double click (edit)
  const handleRowDoubleClick = useCallback(
    (row: TimeRecord) => {
      onRowDoubleClick(row.id)
    },
    [onRowDoubleClick]
  )

  // The selected time record ID is now directly passed as a prop
  const selectedTimeRecordId = selectedRowId

  // Always focus the DataTable to maintain focus during navigation
  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        // Always focus the DataTable, even when there are no records
        dataTableRef.current?.focus()
      },
      getTableElement: () => containerRef.current,
      copySelectedTimeRecord,
    }),
    [copySelectedTimeRecord]
  )

  return (
    <div
      ref={containerRef}
      onKeyDown={handleKeyDown}
      className="outline-none focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
    >
      <DayTimeGraph
        timeRecords={timeRecords}
        currentTime={currentTime}
        startHour={7}
        endHour={20}
        selectedRowId={selectedRowId}
        date={selectedDate}
        onBlockClick={(timeRecordId) => {
          onRowClick(timeRecordId)
        }}
      />

      {/* DataTable for time records - always rendered to maintain focus */}
      <DataTable
        ref={dataTableRef}
        columns={columns}
        data={timeRecords}
        onRowClick={handleRowClick}
        onRowDoubleClick={handleRowDoubleClick}
        getRowId={(record: TimeRecord) => record.id}
        selectedRowId={selectedTimeRecordId}
        highlightedRowId={runningTimerTimerecordId}
        keepFocused={true}
      />
    </div>
  )
}
