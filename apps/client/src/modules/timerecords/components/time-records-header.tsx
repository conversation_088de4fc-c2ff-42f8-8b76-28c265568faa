import type { FC } from 'react'
import { But<PERSON> } from '~/components/ui/button'

interface TimeRecordsHeaderProps {
  onCreateTimer: () => void
  onAddTimeRecord: () => void
  createTimerEnabled: boolean
}

/**
 * Header component for the time records page
 */
export const TimeRecordsHeader: FC<TimeRecordsHeaderProps> = ({
  onCreateTimer,
  onAddTimeRecord,
  createTimerEnabled,
}) => {
  return (
    <div className="flex justify-between items-center mt-6 mb-6 pb-2 border-b">
      <h1 className="text-2xl font-semibold">Time Records</h1>
      <div className="flex gap-2">
        <Button
          onClick={onCreateTimer}
          disabled={!createTimerEnabled}
          variant="default"
        >
          Create Timer (⌘+L)
        </Button>
        <Button onClick={onAddTimeRecord}>Add Time Record</Button>
      </div>
    </div>
  )
}

/**
 * Component for displaying keyboard navigation instructions
 */
export function KeyboardNavigationHelp() {
  return (
    <>
      <p className="text-sm text-muted-foreground mb-4">
        <span className="inline-flex items-center">
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            ←
          </kbd>
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            →
          </kbd>
          Use left/right arrow keys to navigate between dates
        </span>
      </p>
      <p className="text-sm text-muted-foreground mb-4">
        <span className="inline-flex items-center">
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            ↑
          </kbd>
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            ↓
          </kbd>
          Use up/down arrow keys to select rows
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mx-1">
            Enter
          </kbd>
          to perform action on selected row
        </span>
      </p>
      <p className="text-sm text-muted-foreground mb-4">
        <span className="inline-flex items-center">
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            ⌘
          </kbd>
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            E
          </kbd>
          Edit the currently running time record
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mx-1 ml-2">
            ⌘
          </kbd>
          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg mr-1">
            O
          </kbd>
          Stop the current timer
        </span>
      </p>
    </>
  )
}
