import type { ColumnDef } from '@tanstack/react-table'
import type { TimeRecord } from '~/components/time-record-editor'
import type { TimerStruct } from '~/lib/model/app-state'
import {
  formatDate,
  formatDuration,
  formatDurationFromMs,
  formatTime,
} from '~/lib/utils/date-time'

/**
 * Create column definitions for the time records DataTable
 */
export function createTimeRecordColumns(
  runningTimer: TimerStruct | undefined,
  currentTime: number | undefined,
  totalDuration: number
): ColumnDef<TimeRecord>[] {
  const runningTimerTimerecordId = runningTimer?.worklogId

  return [
    {
      accessorKey: 'startTimestamp',
      header: 'Date',
      cell: ({ row }) => formatDate(row.original.startTimestamp),
    },
    {
      accessorKey: 'startTimestamp',
      header: 'Start',
      id: 'startTime',
      cell: ({ row }) => formatTime(row.original.startTimestamp),
    },
    {
      accessorKey: 'endTimestamp',
      header: 'End',
      cell: ({ row }) => {
        const timeRecord = row.original
        const isRunningTimer = timeRecord.id === runningTimerTimerecordId

        return timeRecord.endTimestamp
          ? formatTime(timeRecord.endTimestamp)
          : isRunningTimer
            ? 'Running'
            : '-'
      },
    },
    {
      accessorKey: 'duration',
      header: 'Duration',
      cell: ({ row }) => {
        const timeRecord = row.original
        const isRunningTimer = timeRecord.id === runningTimerTimerecordId

        return isRunningTimer && currentTime
          ? formatDuration(timeRecord.startTimestamp, currentTime)
          : timeRecord.endTimestamp
            ? formatDuration(timeRecord.startTimestamp, timeRecord.endTimestamp)
            : '-'
      },
      footer:
        totalDuration > 0
          ? () => formatDurationFromMs(totalDuration)
          : undefined,
    },
    {
      accessorKey: 'project',
      header: 'Project',
      // size: 200, // Constrain column width
      // maxSize: 250,
      // minSize: 100,
      maxSize: 200,
      enableResizing: true,

      cell: ({ row }) => {
        const timeRecord = row.original
        const projectText = `${timeRecord.task?.project?.name || '-'} / ${timeRecord.task?.name}`
        return (
          <div
            className="truncate overflow-hidden whitespace-nowrap"
            title={projectText}
          >
            {projectText}
          </div>
        )
      },
    },
    {
      accessorKey: 'comment',
      header: 'Comment',
      // size: 200,
      // maxSize: 250,
      // minSize: 100,

      cell: ({ row }) => {
        const text = row.original.comment || '-'
        return (
          <div
            className="truncate overflow-hidden whitespace-nowrap"
            title={text}
          >
            {text}
          </div>
        )
      },
    },
  ]
}
