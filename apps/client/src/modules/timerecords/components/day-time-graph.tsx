import { type FC, useMemo } from 'react'
import type { TimeRecord } from '~/components/time-record-editor'
import { cnByObject } from '~/lib/util/cn-by-object'
import { formatTime } from '~/lib/utils/date-time'

interface DayTimeGraphProps {
  timeRecords: TimeRecord[]
  startHour?: number
  endHour?: number
  currentTime?: number
  selectedRowId?: string
  onBlockClick?: (timeRecordId: string) => void
  date?: string // ISO date string YYYY-MM-DD
}

/**
 * A horizontal bar graph showing time records for a specific day
 */
export const DayTimeGraph: FC<DayTimeGraphProps> = ({
  timeRecords,
  startHour = 7, // Default start hour: 7:00 AM
  endHour = 20, // Default end hour: 8:00 PM
  currentTime = Date.now(),
  selectedRowId,
  onBlockClick,
  date, // ISO date string YYYY-MM-DD
}) => {
  // Calculate the total duration of the graph in milliseconds
  const totalDuration = useMemo(() => {
    const hoursInMs = (endHour - startHour) * 60 * 60 * 1000
    return hoursInMs
  }, [startHour, endHour])

  // Calculate the start timestamp for the graph (selected date at startHour:00)
  const graphStartTimestamp = useMemo(() => {
    // Use the provided date or default to today
    const selectedDate = date ? new Date(date) : new Date()
    selectedDate.setHours(startHour, 0, 0, 0)
    return selectedDate.getTime()
  }, [startHour, date])

  // Calculate the end timestamp for the graph (selected date at endHour:00)
  const graphEndTimestamp = useMemo(() => {
    // Use the provided date or default to today
    const selectedDate = date ? new Date(date) : new Date()
    selectedDate.setHours(endHour, 0, 0, 0)
    return selectedDate.getTime()
  }, [endHour, date])

  // Filter time records for the selected date and within the specified time range
  const selectedDateRecords = useMemo(() => {
    // Use the provided date or default to today
    const selectedDate = date ? new Date(date) : new Date()
    selectedDate.setHours(0, 0, 0, 0)
    const dayStart = selectedDate.getTime()
    const dayEnd = dayStart + 24 * 60 * 60 * 1000

    return timeRecords.filter((record) => {
      return (
        record.startTimestamp >= dayStart &&
        record.startTimestamp < dayEnd &&
        // Ensure the record overlaps with our graph time range
        record.startTimestamp < graphEndTimestamp &&
        (record.endTimestamp === null ||
          record.endTimestamp > graphStartTimestamp)
      )
    })
  }, [timeRecords, graphStartTimestamp, graphEndTimestamp, date])

  // Generate time markers for the graph
  const timeMarkers = useMemo(() => {
    const markers = []
    // Create markers at 1-hour intervals
    for (let hour = startHour; hour <= endHour; hour++) {
      // Use the provided date or default to today
      const markerTime = date ? new Date(date) : new Date()
      markerTime.setHours(hour, 0, 0, 0)

      markers.push({
        hour,
        timestamp: markerTime.getTime(),
        label: formatTime(markerTime.getTime()),
        position: ((hour - startHour) / (endHour - startHour)) * 100,
        isMajor: true,
      })
    }
    return markers
  }, [startHour, endHour, date])

  // Generate segments for each time record
  const timeSegments = useMemo(() => {
    return selectedDateRecords.map((record: TimeRecord) => {
      // Calculate the start position (clamped to graph range)
      const startPos = Math.max(record.startTimestamp, graphStartTimestamp)

      // Calculate the end position (clamped to graph range)
      // If the record is still running, use current time or graph end, whichever is earlier
      const endPos = record.endTimestamp
        ? Math.min(record.endTimestamp, graphEndTimestamp)
        : Math.min(currentTime, graphEndTimestamp)

      // Calculate position and width as percentages
      const positionPercent =
        ((startPos - graphStartTimestamp) / totalDuration) * 100

      const widthPercent = ((endPos - startPos) / totalDuration) * 100

      // Get project color index (for CSS variable)
      // We'll use a modulo operation to cycle through available chart colors
      const projectId = record.task?.project?.id || ''
      const colorIndex =
        Math.abs(
          projectId
            .split('')
            .reduce(
              (acc: number, char: string) => acc + char.charCodeAt(0),
              0
            ) % 5
        ) + 1 // Add 1 to get colors 1-5 instead of 0-4

      return {
        id: record.id,
        startTimestamp: startPos,
        endTimestamp: endPos,
        positionPercent,
        widthPercent,
        colorIndex,
        projectName: record.task?.project?.name || 'Unknown',
        taskName: record.task?.name || 'Unknown',
        isRunning: record.endTimestamp === null,
      }
    })
  }, [
    selectedDateRecords,
    graphStartTimestamp,
    graphEndTimestamp,
    totalDuration,
    currentTime,
  ])

  // Calculate current time marker position if within graph range
  const currentTimeMarker = useMemo(() => {
    if (
      currentTime >= graphStartTimestamp &&
      currentTime <= graphEndTimestamp
    ) {
      const positionPercent =
        ((currentTime - graphStartTimestamp) / totalDuration) * 100

      return {
        timestamp: currentTime,
        positionPercent,
      }
    }
    return null
  }, [currentTime, graphStartTimestamp, graphEndTimestamp, totalDuration])

  // Calculate total time spent for the selected date
  const totalTimeSpent = useMemo(() => {
    return timeSegments.reduce(
      (
        total: number,
        segment: { endTimestamp: number; startTimestamp: number }
      ) => {
        return total + (segment.endTimestamp - segment.startTimestamp)
      },
      0
    )
  }, [timeSegments])

  // Format total time spent
  const formattedTotalTime = useMemo(() => {
    const hours = Math.floor(totalTimeSpent / (1000 * 60 * 60))
    const minutes = Math.floor(
      (totalTimeSpent % (1000 * 60 * 60)) / (1000 * 60)
    )
    return `${hours}h ${minutes}m`
  }, [totalTimeSpent])

  // Format the date for display
  const formattedDate = useMemo(() => {
    if (date) {
      return new Date(date).toLocaleDateString()
    }
    return 'Today'
  }, [date])

  return (
    <div className="w-full mb-4 mt-2">
      <div className="flex justify-between items-center mb-1">
        <div className="text-sm font-medium">
          {formattedDate}'s Timeline ({startHour}:00 - {endHour}:00)
        </div>
        {totalTimeSpent > 0 && (
          <div className="text-sm text-muted-foreground">
            Total: <span className="font-medium">{formattedTotalTime}</span>
          </div>
        )}
      </div>
      <div className="relative h-10 bg-secondary rounded-sm overflow-hidden border border-border shadow-sm">
        {/* Background grid lines for better visibility */}
        <div className="absolute inset-0 w-full h-full">
          {timeMarkers.map((marker) => (
            <div
              key={`grid-line-${marker.hour}`}
              className="absolute border-r border-border/30 h-full"
              style={{ left: `${marker.position}%` }}
            />
          ))}
        </div>

        {/* Time segments */}
        {timeSegments.map((segment) => {
          // Check if this segment corresponds to the selected row
          const isSelected = selectedRowId === segment.id

          return (
            <div
              key={`${segment.id}-${segment.startTimestamp}`}
              className={cnByObject({
                'absolute h-full hover:opacity-80 transition-opacity ring-1 ring-muted-foreground cursor-pointer': true,
                'animate-pulse': segment.isRunning,
                'ring-3 ring-primary z-20 shadow-md border-2 border-primary':
                  isSelected,
              })}
              style={{
                left: `${segment.positionPercent}%`,
                width: `${Math.max(segment.widthPercent, 0.5)}%`, // Ensure minimum width for visibility
                backgroundColor: `var(--chart-${segment.colorIndex})`,
              }}
              title={`${segment.projectName} / ${segment.taskName}: ${formatTime(
                segment.startTimestamp
              )} - ${
                segment.isRunning ? 'Running' : formatTime(segment.endTimestamp)
              }`}
              onClick={() => onBlockClick?.(segment.id)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  onBlockClick?.(segment.id)
                }
              }}
              tabIndex={-1}
              role="button"
              aria-label={`Select time record for ${segment.projectName} / ${segment.taskName}`}
            />
          )
        })}

        {/* Current time marker */}
        {currentTimeMarker && (
          <div
            className="absolute h-full z-10"
            style={{ left: `${currentTimeMarker.positionPercent}%` }}
          >
            <div
              className="absolute h-full w-0.5 bg-primary animate-pulse"
              title={`Current time: ${formatTime(currentTimeMarker.timestamp)}`}
            />
            <div
              className="absolute -top-1 -translate-x-1/2 w-2 h-2 rounded-full bg-primary"
              title={`Current time: ${formatTime(currentTimeMarker.timestamp)}`}
            />
          </div>
        )}

        {/* Time markers */}
        <div className="absolute bottom-0 w-full flex text-xs text-foreground">
          {timeMarkers.map((marker) => (
            <div
              key={marker.hour}
              className="absolute"
              style={{ left: `${marker.position}%` }}
            >
              <div className="relative -left-2 font-medium">{marker.label}</div>
              <div
                className={`absolute h-3 w-0.5 -top-3 ${
                  marker.isMajor ? 'bg-foreground/50' : 'bg-foreground/30'
                }`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
