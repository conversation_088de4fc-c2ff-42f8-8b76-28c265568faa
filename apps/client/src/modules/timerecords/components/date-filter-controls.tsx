import type { FC } from 'react'
import { Button } from '~/components/ui/button'
import { getNextDay, getPreviousDay, getToday } from '~/lib/utils/date-time'

interface DateFilterControlsProps {
  selectedDate: string
  onDateChanged: (date: string) => void
  clearDateFilter: () => void
  showClearButton: boolean
}

/**
 * Component for date filtering controls
 */
export const DateFilterControls: FC<DateFilterControlsProps> = ({
  selectedDate,
  clearDateFilter,
  showClearButton,
  onDateChanged,
}) => {
  const onDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onDateChanged(e.target.value)
  }
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="flex items-center gap-2">
        <label htmlFor="date-filter" className="font-medium text-sm">
          Filter by date:
        </label>
        <input
          id="date-filter"
          type="date"
          value={selectedDate}
          onChange={onDateChange}
          className="border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
        />
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            onDateChanged(getPreviousDay(selectedDate))
          }}
        >
          ← Previous Day
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            onDateChanged(getToday())
          }}
        >
          Today
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            onDateChanged(getNextDay(selectedDate))
          }}
        >
          Next Day →
        </Button>
      </div>
      {showClearButton && (
        <Button variant="outline" size="sm" onClick={clearDateFilter}>
          Clear Filter
        </Button>
      )}
    </div>
  )
}
