import * as v from 'valibot'

// Define Valibot schema for time record validation
export const TimeRecordSchema = v.object({
  id: v.string(),
  taskId: v.pipe(v.string('Task is required'), v.minLength(36)),
  startTimestamp: v.number('Start time is required'),
  endTimestamp: v.nullable(v.number()),
  comment: v.optional(v.string(), ''),
})

export type TimeRecordInput = v.InferInput<typeof TimeRecordSchema>
