import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { useQuery } from '@rocicorp/zero/react'
import { useCallback, useEffect, useMemo } from 'react'
import type { SlimTimeRecord } from '~/components/time-record-editor'
import { createTimeRecord, updateTimeRecord } from '~/lib/model/app-state'
import { RemoteServiceManager } from '~/lib/services/remote-service-manager'
import type { TimeRecordInput } from '../time-record-model'

interface UseTimeRecordsOptions {
  z: Zero<Schema>
  selectedStart: number
  selectedEnd: number
}

/**
 * Hook for fetching and managing time records
 * Includes integration with remote service adapters
 */
export function useTimeRecords({
  z,
  selectedStart,
  selectedEnd,
}: UseTimeRecordsOptions) {
  // Create a remote service manager instance
  const remoteServiceManager = useMemo(() => new RemoteServiceManager(z), [z])

  // Fetch time records for the selected date range
  const [timeRecords] = useQuery(
    z.query.timerecords
      .related('task', (q) =>
        q.related('project', (q) => q.related('customer'))
      )
      .where(({ cmp, and }) => {
        return and(
          cmp('startTimestamp', '>=', selectedStart),
          cmp('startTimestamp', '<=', selectedEnd)
        )
      })
      .orderBy('startTimestamp', 'asc')
      .limit(100),
    {
      ttl: 'forever',
    }
  )

  useEffect(() => {
    console.log('useTimeRecords: timerecords changed', timeRecords)
  }, [timeRecords])

  /**
   * Save a time record (create or update) and sync with remote service
   */
  const saveTimeRecord = useCallback(
    async (timeRecord: TimeRecordInput) => {
      try {
        // First save the time record locally
        if (timeRecords.some((record) => record.id === timeRecord.id)) {
          await updateTimeRecord(z, {
            id: timeRecord.id,
            taskId: timeRecord.taskId,
            startTimestamp: timeRecord.startTimestamp,
            endTimestamp: timeRecord.endTimestamp,
            comment: timeRecord.comment,
          })
        } else {
          await createTimeRecord(z, {
            id: timeRecord.id,
            taskId: timeRecord.taskId,
            startTimestamp: timeRecord.startTimestamp,
            endTimestamp: timeRecord.endTimestamp,
            comment: timeRecord.comment,
          })
        }

        // Then sync with remote service
        // Get the full time record with task information
        const fullTimeRecord = await z.query.timerecords
          .where('id', '=', timeRecord.id)
          .related('task', (q) => q.related('project'))
          .one()
          .run()

        if (fullTimeRecord) {
          // Sync with remote service
          await remoteServiceManager.syncWorklog(fullTimeRecord)
        }
      } catch (error) {
        console.error('Error saving time record:', error)
      }
    },
    [timeRecords, z, remoteServiceManager]
  )

  /**
   * Delete a time record and remove from remote service
   */
  const deleteTimeRecord = useCallback(
    async (timeRecord: SlimTimeRecord) => {
      try {
        // Use the database service to delete the time record and sync with remote service
        const { dbDeleteTimeRecord } = await import(
          '~/lib/services/database-service'
        )
        await dbDeleteTimeRecord(z, timeRecord.id)
      } catch (error) {
        console.error('Error deleting time record:', error)
      }
    },
    [z]
  )

  return {
    timeRecords,
    saveTimeRecord,
    deleteTimeRecord,
  }
}
