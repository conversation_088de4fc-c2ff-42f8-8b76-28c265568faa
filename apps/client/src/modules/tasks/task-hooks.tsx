import type { Schema, Task } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { uuidv7 } from 'uuidv7'

// Custom hook for task management
export function useTaskManagement(z: Zero<Schema>, existingTasks: Task[]) {
  // Handle save
  const handleSave = async (task: Task) => {
    try {
      if (existingTasks.some((t) => t.id === task.id)) {
        // Update existing task
        await z.mutate.tasks.update({
          id: task.id,
          name: task.name,
          projectId: task.projectId,
          status: task.status,
          defaultTask: task.defaultTask,
          pinned: task.pinned,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new task
        await z.mutate.tasks.insert({
          ...task,
          id: task.id ?? uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving task:', error)
    }
  }

  // Handle delete
  const handleDelete = async (task: Task) => {
    try {
      await z.mutate.tasks.delete({
        id: task.id,
      })
    } catch (error) {
      console.error('Error deleting task:', error)
    }
  }

  return { handleSave, handleDelete }
}
