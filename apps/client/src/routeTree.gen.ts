/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthImport } from './routes/_auth'
import { Route as AuthIndexImport } from './routes/_auth/index'
import { Route as AuthReportsRouteImport } from './routes/_auth/reports/route'
import { Route as AuthAdminRouteImport } from './routes/_auth/admin/route'
import { Route as AuthTimersIndexImport } from './routes/_auth/timers/index'
import { Route as AuthTimeRecordsIndexImport } from './routes/_auth/time-records/index'
import { Route as AuthReportsIndexImport } from './routes/_auth/reports/index'
import { Route as AuthAdminIndexImport } from './routes/_auth/admin/index'
import { Route as AuthReportsTimerecordDetailsImport } from './routes/_auth/reports/timerecord-details'
import { Route as AuthReportsTimerecordAggregateImport } from './routes/_auth/reports/timerecord-aggregate'
import { Route as AuthAdminUsersImport } from './routes/_auth/admin/users'
import { Route as AuthAdminRemoteServiceImport } from './routes/_auth/admin/remote-service'
import { Route as AuthAdminProjectsImport } from './routes/_auth/admin/projects'
import { Route as AuthAdminProjectcatalogsImport } from './routes/_auth/admin/projectcatalogs'
import { Route as AuthAdminImportImport } from './routes/_auth/admin/import'
import { Route as AuthAdminCustomersImport } from './routes/_auth/admin/customers'
import { Route as AuthAdminAccountsImport } from './routes/_auth/admin/accounts'
import { Route as AuthAdminTasksIndexImport } from './routes/_auth/admin/tasks/index'
import { Route as AuthAdminTaskcatalogsIndexImport } from './routes/_auth/admin/taskcatalogs/index'
import { Route as AuthAdminTasksTaskIdImport } from './routes/_auth/admin/tasks/$taskId'
import { Route as AuthAdminTaskcatalogsTaskCatalogIdImport } from './routes/_auth/admin/taskcatalogs/$taskCatalogId'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const AuthIndexRoute = AuthIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthReportsRouteRoute = AuthReportsRouteImport.update({
  id: '/reports',
  path: '/reports',
  getParentRoute: () => AuthRoute,
} as any)

const AuthAdminRouteRoute = AuthAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthRoute,
} as any)

const AuthTimersIndexRoute = AuthTimersIndexImport.update({
  id: '/timers/',
  path: '/timers/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthTimeRecordsIndexRoute = AuthTimeRecordsIndexImport.update({
  id: '/time-records/',
  path: '/time-records/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthReportsIndexRoute = AuthReportsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthReportsRouteRoute,
} as any)

const AuthAdminIndexRoute = AuthAdminIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthReportsTimerecordDetailsRoute =
  AuthReportsTimerecordDetailsImport.update({
    id: '/timerecord-details',
    path: '/timerecord-details',
    getParentRoute: () => AuthReportsRouteRoute,
  } as any)

const AuthReportsTimerecordAggregateRoute =
  AuthReportsTimerecordAggregateImport.update({
    id: '/timerecord-aggregate',
    path: '/timerecord-aggregate',
    getParentRoute: () => AuthReportsRouteRoute,
  } as any)

const AuthAdminUsersRoute = AuthAdminUsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminRemoteServiceRoute = AuthAdminRemoteServiceImport.update({
  id: '/remote-service',
  path: '/remote-service',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminProjectsRoute = AuthAdminProjectsImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminProjectcatalogsRoute = AuthAdminProjectcatalogsImport.update({
  id: '/projectcatalogs',
  path: '/projectcatalogs',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminImportRoute = AuthAdminImportImport.update({
  id: '/import',
  path: '/import',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminCustomersRoute = AuthAdminCustomersImport.update({
  id: '/customers',
  path: '/customers',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminAccountsRoute = AuthAdminAccountsImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminTasksIndexRoute = AuthAdminTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminTaskcatalogsIndexRoute = AuthAdminTaskcatalogsIndexImport.update(
  {
    id: '/taskcatalogs/',
    path: '/taskcatalogs/',
    getParentRoute: () => AuthAdminRouteRoute,
  } as any,
)

const AuthAdminTasksTaskIdRoute = AuthAdminTasksTaskIdImport.update({
  id: '/tasks/$taskId',
  path: '/tasks/$taskId',
  getParentRoute: () => AuthAdminRouteRoute,
} as any)

const AuthAdminTaskcatalogsTaskCatalogIdRoute =
  AuthAdminTaskcatalogsTaskCatalogIdImport.update({
    id: '/taskcatalogs/$taskCatalogId',
    path: '/taskcatalogs/$taskCatalogId',
    getParentRoute: () => AuthAdminRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_auth/admin': {
      id: '/_auth/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthAdminRouteImport
      parentRoute: typeof AuthImport
    }
    '/_auth/reports': {
      id: '/_auth/reports'
      path: '/reports'
      fullPath: '/reports'
      preLoaderRoute: typeof AuthReportsRouteImport
      parentRoute: typeof AuthImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexImport
      parentRoute: typeof AuthImport
    }
    '/_auth/admin/accounts': {
      id: '/_auth/admin/accounts'
      path: '/accounts'
      fullPath: '/admin/accounts'
      preLoaderRoute: typeof AuthAdminAccountsImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/customers': {
      id: '/_auth/admin/customers'
      path: '/customers'
      fullPath: '/admin/customers'
      preLoaderRoute: typeof AuthAdminCustomersImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/import': {
      id: '/_auth/admin/import'
      path: '/import'
      fullPath: '/admin/import'
      preLoaderRoute: typeof AuthAdminImportImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/projectcatalogs': {
      id: '/_auth/admin/projectcatalogs'
      path: '/projectcatalogs'
      fullPath: '/admin/projectcatalogs'
      preLoaderRoute: typeof AuthAdminProjectcatalogsImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/projects': {
      id: '/_auth/admin/projects'
      path: '/projects'
      fullPath: '/admin/projects'
      preLoaderRoute: typeof AuthAdminProjectsImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/remote-service': {
      id: '/_auth/admin/remote-service'
      path: '/remote-service'
      fullPath: '/admin/remote-service'
      preLoaderRoute: typeof AuthAdminRemoteServiceImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/users': {
      id: '/_auth/admin/users'
      path: '/users'
      fullPath: '/admin/users'
      preLoaderRoute: typeof AuthAdminUsersImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/reports/timerecord-aggregate': {
      id: '/_auth/reports/timerecord-aggregate'
      path: '/timerecord-aggregate'
      fullPath: '/reports/timerecord-aggregate'
      preLoaderRoute: typeof AuthReportsTimerecordAggregateImport
      parentRoute: typeof AuthReportsRouteImport
    }
    '/_auth/reports/timerecord-details': {
      id: '/_auth/reports/timerecord-details'
      path: '/timerecord-details'
      fullPath: '/reports/timerecord-details'
      preLoaderRoute: typeof AuthReportsTimerecordDetailsImport
      parentRoute: typeof AuthReportsRouteImport
    }
    '/_auth/admin/': {
      id: '/_auth/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AuthAdminIndexImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/reports/': {
      id: '/_auth/reports/'
      path: '/'
      fullPath: '/reports/'
      preLoaderRoute: typeof AuthReportsIndexImport
      parentRoute: typeof AuthReportsRouteImport
    }
    '/_auth/time-records/': {
      id: '/_auth/time-records/'
      path: '/time-records'
      fullPath: '/time-records'
      preLoaderRoute: typeof AuthTimeRecordsIndexImport
      parentRoute: typeof AuthImport
    }
    '/_auth/timers/': {
      id: '/_auth/timers/'
      path: '/timers'
      fullPath: '/timers'
      preLoaderRoute: typeof AuthTimersIndexImport
      parentRoute: typeof AuthImport
    }
    '/_auth/admin/taskcatalogs/$taskCatalogId': {
      id: '/_auth/admin/taskcatalogs/$taskCatalogId'
      path: '/taskcatalogs/$taskCatalogId'
      fullPath: '/admin/taskcatalogs/$taskCatalogId'
      preLoaderRoute: typeof AuthAdminTaskcatalogsTaskCatalogIdImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/tasks/$taskId': {
      id: '/_auth/admin/tasks/$taskId'
      path: '/tasks/$taskId'
      fullPath: '/admin/tasks/$taskId'
      preLoaderRoute: typeof AuthAdminTasksTaskIdImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/taskcatalogs/': {
      id: '/_auth/admin/taskcatalogs/'
      path: '/taskcatalogs'
      fullPath: '/admin/taskcatalogs'
      preLoaderRoute: typeof AuthAdminTaskcatalogsIndexImport
      parentRoute: typeof AuthAdminRouteImport
    }
    '/_auth/admin/tasks/': {
      id: '/_auth/admin/tasks/'
      path: '/tasks'
      fullPath: '/admin/tasks'
      preLoaderRoute: typeof AuthAdminTasksIndexImport
      parentRoute: typeof AuthAdminRouteImport
    }
  }
}

// Create and export the route tree

interface AuthAdminRouteRouteChildren {
  AuthAdminAccountsRoute: typeof AuthAdminAccountsRoute
  AuthAdminCustomersRoute: typeof AuthAdminCustomersRoute
  AuthAdminImportRoute: typeof AuthAdminImportRoute
  AuthAdminProjectcatalogsRoute: typeof AuthAdminProjectcatalogsRoute
  AuthAdminProjectsRoute: typeof AuthAdminProjectsRoute
  AuthAdminRemoteServiceRoute: typeof AuthAdminRemoteServiceRoute
  AuthAdminUsersRoute: typeof AuthAdminUsersRoute
  AuthAdminIndexRoute: typeof AuthAdminIndexRoute
  AuthAdminTaskcatalogsTaskCatalogIdRoute: typeof AuthAdminTaskcatalogsTaskCatalogIdRoute
  AuthAdminTasksTaskIdRoute: typeof AuthAdminTasksTaskIdRoute
  AuthAdminTaskcatalogsIndexRoute: typeof AuthAdminTaskcatalogsIndexRoute
  AuthAdminTasksIndexRoute: typeof AuthAdminTasksIndexRoute
}

const AuthAdminRouteRouteChildren: AuthAdminRouteRouteChildren = {
  AuthAdminAccountsRoute: AuthAdminAccountsRoute,
  AuthAdminCustomersRoute: AuthAdminCustomersRoute,
  AuthAdminImportRoute: AuthAdminImportRoute,
  AuthAdminProjectcatalogsRoute: AuthAdminProjectcatalogsRoute,
  AuthAdminProjectsRoute: AuthAdminProjectsRoute,
  AuthAdminRemoteServiceRoute: AuthAdminRemoteServiceRoute,
  AuthAdminUsersRoute: AuthAdminUsersRoute,
  AuthAdminIndexRoute: AuthAdminIndexRoute,
  AuthAdminTaskcatalogsTaskCatalogIdRoute:
    AuthAdminTaskcatalogsTaskCatalogIdRoute,
  AuthAdminTasksTaskIdRoute: AuthAdminTasksTaskIdRoute,
  AuthAdminTaskcatalogsIndexRoute: AuthAdminTaskcatalogsIndexRoute,
  AuthAdminTasksIndexRoute: AuthAdminTasksIndexRoute,
}

const AuthAdminRouteRouteWithChildren = AuthAdminRouteRoute._addFileChildren(
  AuthAdminRouteRouteChildren,
)

interface AuthReportsRouteRouteChildren {
  AuthReportsTimerecordAggregateRoute: typeof AuthReportsTimerecordAggregateRoute
  AuthReportsTimerecordDetailsRoute: typeof AuthReportsTimerecordDetailsRoute
  AuthReportsIndexRoute: typeof AuthReportsIndexRoute
}

const AuthReportsRouteRouteChildren: AuthReportsRouteRouteChildren = {
  AuthReportsTimerecordAggregateRoute: AuthReportsTimerecordAggregateRoute,
  AuthReportsTimerecordDetailsRoute: AuthReportsTimerecordDetailsRoute,
  AuthReportsIndexRoute: AuthReportsIndexRoute,
}

const AuthReportsRouteRouteWithChildren =
  AuthReportsRouteRoute._addFileChildren(AuthReportsRouteRouteChildren)

interface AuthRouteChildren {
  AuthAdminRouteRoute: typeof AuthAdminRouteRouteWithChildren
  AuthReportsRouteRoute: typeof AuthReportsRouteRouteWithChildren
  AuthIndexRoute: typeof AuthIndexRoute
  AuthTimeRecordsIndexRoute: typeof AuthTimeRecordsIndexRoute
  AuthTimersIndexRoute: typeof AuthTimersIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthAdminRouteRoute: AuthAdminRouteRouteWithChildren,
  AuthReportsRouteRoute: AuthReportsRouteRouteWithChildren,
  AuthIndexRoute: AuthIndexRoute,
  AuthTimeRecordsIndexRoute: AuthTimeRecordsIndexRoute,
  AuthTimersIndexRoute: AuthTimersIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthAdminRouteRouteWithChildren
  '/reports': typeof AuthReportsRouteRouteWithChildren
  '/': typeof AuthIndexRoute
  '/admin/accounts': typeof AuthAdminAccountsRoute
  '/admin/customers': typeof AuthAdminCustomersRoute
  '/admin/import': typeof AuthAdminImportRoute
  '/admin/projectcatalogs': typeof AuthAdminProjectcatalogsRoute
  '/admin/projects': typeof AuthAdminProjectsRoute
  '/admin/remote-service': typeof AuthAdminRemoteServiceRoute
  '/admin/users': typeof AuthAdminUsersRoute
  '/reports/timerecord-aggregate': typeof AuthReportsTimerecordAggregateRoute
  '/reports/timerecord-details': typeof AuthReportsTimerecordDetailsRoute
  '/admin/': typeof AuthAdminIndexRoute
  '/reports/': typeof AuthReportsIndexRoute
  '/time-records': typeof AuthTimeRecordsIndexRoute
  '/timers': typeof AuthTimersIndexRoute
  '/admin/taskcatalogs/$taskCatalogId': typeof AuthAdminTaskcatalogsTaskCatalogIdRoute
  '/admin/tasks/$taskId': typeof AuthAdminTasksTaskIdRoute
  '/admin/taskcatalogs': typeof AuthAdminTaskcatalogsIndexRoute
  '/admin/tasks': typeof AuthAdminTasksIndexRoute
}

export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/': typeof AuthIndexRoute
  '/admin/accounts': typeof AuthAdminAccountsRoute
  '/admin/customers': typeof AuthAdminCustomersRoute
  '/admin/import': typeof AuthAdminImportRoute
  '/admin/projectcatalogs': typeof AuthAdminProjectcatalogsRoute
  '/admin/projects': typeof AuthAdminProjectsRoute
  '/admin/remote-service': typeof AuthAdminRemoteServiceRoute
  '/admin/users': typeof AuthAdminUsersRoute
  '/reports/timerecord-aggregate': typeof AuthReportsTimerecordAggregateRoute
  '/reports/timerecord-details': typeof AuthReportsTimerecordDetailsRoute
  '/admin': typeof AuthAdminIndexRoute
  '/reports': typeof AuthReportsIndexRoute
  '/time-records': typeof AuthTimeRecordsIndexRoute
  '/timers': typeof AuthTimersIndexRoute
  '/admin/taskcatalogs/$taskCatalogId': typeof AuthAdminTaskcatalogsTaskCatalogIdRoute
  '/admin/tasks/$taskId': typeof AuthAdminTasksTaskIdRoute
  '/admin/taskcatalogs': typeof AuthAdminTaskcatalogsIndexRoute
  '/admin/tasks': typeof AuthAdminTasksIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/_auth/admin': typeof AuthAdminRouteRouteWithChildren
  '/_auth/reports': typeof AuthReportsRouteRouteWithChildren
  '/_auth/': typeof AuthIndexRoute
  '/_auth/admin/accounts': typeof AuthAdminAccountsRoute
  '/_auth/admin/customers': typeof AuthAdminCustomersRoute
  '/_auth/admin/import': typeof AuthAdminImportRoute
  '/_auth/admin/projectcatalogs': typeof AuthAdminProjectcatalogsRoute
  '/_auth/admin/projects': typeof AuthAdminProjectsRoute
  '/_auth/admin/remote-service': typeof AuthAdminRemoteServiceRoute
  '/_auth/admin/users': typeof AuthAdminUsersRoute
  '/_auth/reports/timerecord-aggregate': typeof AuthReportsTimerecordAggregateRoute
  '/_auth/reports/timerecord-details': typeof AuthReportsTimerecordDetailsRoute
  '/_auth/admin/': typeof AuthAdminIndexRoute
  '/_auth/reports/': typeof AuthReportsIndexRoute
  '/_auth/time-records/': typeof AuthTimeRecordsIndexRoute
  '/_auth/timers/': typeof AuthTimersIndexRoute
  '/_auth/admin/taskcatalogs/$taskCatalogId': typeof AuthAdminTaskcatalogsTaskCatalogIdRoute
  '/_auth/admin/tasks/$taskId': typeof AuthAdminTasksTaskIdRoute
  '/_auth/admin/taskcatalogs/': typeof AuthAdminTaskcatalogsIndexRoute
  '/_auth/admin/tasks/': typeof AuthAdminTasksIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/admin'
    | '/reports'
    | '/'
    | '/admin/accounts'
    | '/admin/customers'
    | '/admin/import'
    | '/admin/projectcatalogs'
    | '/admin/projects'
    | '/admin/remote-service'
    | '/admin/users'
    | '/reports/timerecord-aggregate'
    | '/reports/timerecord-details'
    | '/admin/'
    | '/reports/'
    | '/time-records'
    | '/timers'
    | '/admin/taskcatalogs/$taskCatalogId'
    | '/admin/tasks/$taskId'
    | '/admin/taskcatalogs'
    | '/admin/tasks'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/'
    | '/admin/accounts'
    | '/admin/customers'
    | '/admin/import'
    | '/admin/projectcatalogs'
    | '/admin/projects'
    | '/admin/remote-service'
    | '/admin/users'
    | '/reports/timerecord-aggregate'
    | '/reports/timerecord-details'
    | '/admin'
    | '/reports'
    | '/time-records'
    | '/timers'
    | '/admin/taskcatalogs/$taskCatalogId'
    | '/admin/tasks/$taskId'
    | '/admin/taskcatalogs'
    | '/admin/tasks'
  id:
    | '__root__'
    | '/_auth'
    | '/login'
    | '/_auth/admin'
    | '/_auth/reports'
    | '/_auth/'
    | '/_auth/admin/accounts'
    | '/_auth/admin/customers'
    | '/_auth/admin/import'
    | '/_auth/admin/projectcatalogs'
    | '/_auth/admin/projects'
    | '/_auth/admin/remote-service'
    | '/_auth/admin/users'
    | '/_auth/reports/timerecord-aggregate'
    | '/_auth/reports/timerecord-details'
    | '/_auth/admin/'
    | '/_auth/reports/'
    | '/_auth/time-records/'
    | '/_auth/timers/'
    | '/_auth/admin/taskcatalogs/$taskCatalogId'
    | '/_auth/admin/tasks/$taskId'
    | '/_auth/admin/taskcatalogs/'
    | '/_auth/admin/tasks/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/login"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/admin",
        "/_auth/reports",
        "/_auth/",
        "/_auth/time-records/",
        "/_auth/timers/"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_auth/admin": {
      "filePath": "_auth/admin/route.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/admin/accounts",
        "/_auth/admin/customers",
        "/_auth/admin/import",
        "/_auth/admin/projectcatalogs",
        "/_auth/admin/projects",
        "/_auth/admin/remote-service",
        "/_auth/admin/users",
        "/_auth/admin/",
        "/_auth/admin/taskcatalogs/$taskCatalogId",
        "/_auth/admin/tasks/$taskId",
        "/_auth/admin/taskcatalogs/",
        "/_auth/admin/tasks/"
      ]
    },
    "/_auth/reports": {
      "filePath": "_auth/reports/route.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/reports/timerecord-aggregate",
        "/_auth/reports/timerecord-details",
        "/_auth/reports/"
      ]
    },
    "/_auth/": {
      "filePath": "_auth/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/admin/accounts": {
      "filePath": "_auth/admin/accounts.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/customers": {
      "filePath": "_auth/admin/customers.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/import": {
      "filePath": "_auth/admin/import.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/projectcatalogs": {
      "filePath": "_auth/admin/projectcatalogs.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/projects": {
      "filePath": "_auth/admin/projects.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/remote-service": {
      "filePath": "_auth/admin/remote-service.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/users": {
      "filePath": "_auth/admin/users.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/reports/timerecord-aggregate": {
      "filePath": "_auth/reports/timerecord-aggregate.tsx",
      "parent": "/_auth/reports"
    },
    "/_auth/reports/timerecord-details": {
      "filePath": "_auth/reports/timerecord-details.tsx",
      "parent": "/_auth/reports"
    },
    "/_auth/admin/": {
      "filePath": "_auth/admin/index.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/reports/": {
      "filePath": "_auth/reports/index.tsx",
      "parent": "/_auth/reports"
    },
    "/_auth/time-records/": {
      "filePath": "_auth/time-records/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/timers/": {
      "filePath": "_auth/timers/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/admin/taskcatalogs/$taskCatalogId": {
      "filePath": "_auth/admin/taskcatalogs/$taskCatalogId.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/tasks/$taskId": {
      "filePath": "_auth/admin/tasks/$taskId.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/taskcatalogs/": {
      "filePath": "_auth/admin/taskcatalogs/index.tsx",
      "parent": "/_auth/admin"
    },
    "/_auth/admin/tasks/": {
      "filePath": "_auth/admin/tasks/index.tsx",
      "parent": "/_auth/admin"
    }
  }
}
ROUTE_MANIFEST_END */
