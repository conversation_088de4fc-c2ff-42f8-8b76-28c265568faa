import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { type FC, useEffect, useState } from 'react'
import { useRefify } from '~/lib/hooks/use-refify'
import type { TimeTrackerState, TimerStruct } from '~/lib/model/app-state'
import { dbCreateAppState } from '~/lib/services/database-service'
import { clientId } from '~/modules/state/active-client'

export const StateInitializer: FC = () => {
  const z = useZero<Schema>()

  const [runningTimersResult, runningTimersDetails] = useQuery(
    z.query.timers
      .where('status', '=', 'running')
      .related('timerecord', (q) => q.related('task'))
      .limit(1),
    {
      ttl: 0, // No caching, always get the latest
    }
  )
  const [appStateResult, appStateDetails] = useQuery(
    z.query.appState
      .related('runningTimer', (q) =>
        q.related('timerecord', (q) => q.related('task'))
      )
      .where('id', '=', z.userID)
      .one(),

    {
      ttl: 'forever',
    }
  )

  const appStateComplete = appStateDetails.type === 'complete'
  const [initialized, setInitialized] = useState(false)
  const runningTimersComplete = runningTimersDetails.type === 'complete'
  const zRef = useRefify(z)

  console.log('render state initializer', {
    appStateComplete,
    runningTimersComplete,
    initialized,
    appStateResult,
    runningTimersResult,
  })

  useEffect(() => {
    if (
      !initialized &&
      appStateComplete &&
      !appStateResult &&
      runningTimersComplete
    ) {
      console.log('no app state yet, creating one')
      const runningTimer = runningTimersResult[0]
      // no app state yet
      setInitialized(true)
      const rtStruct: TimerStruct | undefined = runningTimer
        ? {
            id: runningTimer.id,
            worklogId: runningTimer.worklogId,
            // taskId: runningTimer.timerecord?.taskId,
            // projectId: runningTimer.timerecord?.task?.projectId,
            // startTimestamp: runningTimer.startTimestamp,
            // endTimestamp: runningTimer.endTimestamp,
            status: runningTimer.status,
          }
        : undefined

      // calculate the hash from the state
      const newAppState: TimeTrackerState = {
        runningTimer: rtStruct,
        previousTimerRunning: !!rtStruct,
        isEditDialogOpen: false,
        timestampOfIdleTimeStart: undefined,
        showingTimeoutMessage: false,
        selectedTaskId: undefined,
        activeClientId: clientId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }
      dbCreateAppState(zRef.current, newAppState).catch((error) => {
        console.error('Error creating app state:', error)
      })
    }
  }, [
    zRef,
    appStateComplete,
    runningTimersComplete,
    initialized,
    appStateResult,
    runningTimersResult,
  ])

  return null
}
