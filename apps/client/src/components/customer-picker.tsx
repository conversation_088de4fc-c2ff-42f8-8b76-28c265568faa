import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { type FC, useEffect, useMemo, useRef, useState } from 'react'

import { cn } from '~/lib/utils'
import { Combobox, CommandOptions } from './combobox'

export interface CustomerPickerProps {
  value?: string
  onChange?: (taskId: string) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  display?: 'combobox' | 'search'
}

export const CustomerPicker: FC<CustomerPickerProps> = ({
  value,
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  display = 'combobox',
}) => {
  const z = useZero<Schema>()
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  const shouldUseSearch = debouncedSearchQuery.length >= 2

  const query = useMemo(() => {
    let q = z.query.customers

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or }) => {
        const conditions = [cmp('name', 'ILIKE', searchPattern)]
        if (value) {
          conditions.push(cmp('id', '=', value))
        }
        return or(...conditions)
      })
    } else {
      q = q.orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, value])

  const [customers = [], customerQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const customerOptions = useMemo(
    () =>
      customers.map((it) => ({ value: it.id, label: formatCustomerLabel(it) })),
    [customers]
  )

  return (
    <div className={cn('relative', className)}>
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          options={customerOptions}
          shouldFilter={false}
          onBlur={onBlur}
          loading={customerQueryDetails.type !== 'complete'}
        />
      ) : (
        <CommandOptions
          options={customerOptions}
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          shouldFilter={false}
          onBlur={onBlur}
          loading={customerQueryDetails.type !== 'complete'}
        />
      )}
    </div>
  )
}

function formatCustomerLabel(customer: {
  name: string
}): string {
  return customer.name
}
