import React, {
  type FC,
  type Ref,
  useCallback,
  useEffect,
  useMemo,
} from 'react'
import { type Options, useTimescape } from 'timescape/react'

import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { isNonEmptyArray } from '@tanstack/react-form'
import { useRefify } from '~/lib/hooks/use-refify'
// @source: https://github.com/dan-lee/timescape?tab=readme-ov-file

const timePickerInputBase =
  'p-1 inline tabular-nums h-fit border-none outline-none select-none content-box caret-transparent rounded-sm min-w-8 text-center focus:bg-foreground/20 dark:focus:bg-foreground/50 focus-visible:ring-0 focus-visible:outline-none'
const timePickerSeparatorBase = 'text-xs text-gray-400'

type DateFormat = 'days' | 'months' | 'years'
type TimeFormat = 'hours' | 'minutes' | 'seconds' | 'am/pm'

type DateTimeArray<T extends DateFormat | TimeFormat> = T[]
export type DateTimeFormatDefaults = [
  DateTimeArray<DateFormat>,
  DateTimeArray<TimeFormat>,
]

export const DATETIME_DEFAULTS = [
  ['days', 'months', 'years'],
  ['hours', 'minutes'],
] as DateTimeFormatDefaults

export const DATE_DEFAULTS = [
  ['months', 'days', 'years'],
  [],
] as DateTimeFormatDefaults

type TimescapeReturn = ReturnType<typeof useTimescape>
type InputPlaceholders = Record<DateFormat | TimeFormat, string>

const INPUT_PLACEHOLDERS: InputPlaceholders = {
  months: 'MM',
  days: 'DD',
  years: 'YYYY',
  hours: 'HH',
  minutes: 'MM',
  seconds: 'SS',
  'am/pm': 'AM/PM',
}

/**
 * Date time picker Docs: {@link: https://shadcn-extension.vercel.app/docs/otp-input}
 */

interface DatetimeGridProps {
  format: DateTimeFormatDefaults
  className?: string
  timescape: Pick<TimescapeReturn, 'getRootProps' | 'getInputProps'>
  placeholders: InputPlaceholders
  onBlur?: () => void
  ref?: Ref<HTMLDivElement>
}
// DatetimeGrid component using React 19 style
const DatetimeGrid: FC<DatetimeGridProps> = ({
  format,
  className,
  timescape,
  placeholders,
  onBlur,
  ref,
}) => {
  return (
    <div
      className={cn(
        'flex items-center w-fit p-1 border-2',
        className,
        'border-input rounded-md gap-1 selection:bg-transparent selection:text-foreground'
      )}
      {...timescape.getRootProps()}
      ref={ref}
    >
      {isNonEmptyArray(format)
        ? format.map((group, i) => (
            <React.Fragment key={i === 0 ? 'dates' : 'times'}>
              {isNonEmptyArray(group)
                ? group.map((unit, j) => (
                    <React.Fragment key={unit}>
                      <Input
                        className={cn(timePickerInputBase, 'min-w-8', {
                          'min-w-12': unit === 'years',
                          'bg-foreground/15': unit === 'am/pm',
                        })}
                        {...timescape.getInputProps(unit)}
                        placeholder={placeholders[unit]}
                        onBlur={onBlur}
                        // ref={i === 0 && j === 0 ? ref : null}
                      />
                      {i === 0 && j < group.length - 1 ? (
                        // date separator
                        <span className={timePickerSeparatorBase}>/</span>
                      ) : (
                        j < group.length - 2 && (
                          // time separator
                          <span className={timePickerSeparatorBase}>:</span>
                        )
                      )}
                    </React.Fragment>
                  ))
                : null}
              {isNonEmptyArray(format[1]) && !i ? (
                // date-time separator - only if both date and time are present
                <span
                  className={cn(timePickerSeparatorBase, 'opacity-30 text-xl')}
                >
                  |
                </span>
              ) : null}
            </React.Fragment>
          ))
        : null}
    </div>
  )
}

interface DateTimeInputProps {
  name?: string
  value?: Date | number
  format: DateTimeFormatDefaults
  placeholders?: InputPlaceholders
  onChange?: Options['onChangeDate']
  onBlur?: () => void
  dtOptions?: Options
  className?: string
  ref?: Ref<HTMLDivElement>
}

const DEFAULT_TS_OPTIONS = {
  date: new Date(),
  hour12: false,
}

// DatetimeInput component using React 19 style
export const DatetimeInput: FC<DateTimeInputProps> = ({
  value: tempVal,
  format = DATETIME_DEFAULTS,
  placeholders,
  dtOptions = DEFAULT_TS_OPTIONS,
  onChange,
  onBlur,
  className,
  name,
  ref,
}) => {
  const value = useMemo(() => {
    if (!tempVal) {
      return undefined
    }
    return typeof tempVal === 'number' ? new Date(tempVal) : tempVal
  }, [tempVal])

  console.log('render datetime input', { value, name })
  const onChangeRef = useRefify(onChange)

  const handleDateChange = useCallback(
    (nextDate: Date | undefined) => {
      const localOnChange = onChangeRef.current
      localOnChange?.(nextDate)
    },
    [onChangeRef]
  )

  const dtOpts = useMemo(() => {
    return {
      ...dtOptions,
      ...(value && { date: value }),
      onChangeDate: handleDateChange,
    }
  }, [dtOptions, value, handleDateChange])

  console.log('use timescape', name, dtOpts)
  const timescape = useTimescape(dtOpts)

  const actValRef = useRefify(timescape.options.date)
  const timeScapeUpdate = useRefify(timescape.update)
  useEffect(() => {
    if (value !== actValRef.current) {
      timeScapeUpdate.current({ date: value })
    }
  }, [value, timeScapeUpdate, actValRef])

  return (
    <DatetimeGrid
      ref={ref}
      format={format}
      className={className}
      timescape={timescape}
      placeholders={placeholders ?? INPUT_PLACEHOLDERS}
      onBlur={onBlur}
    />
  )
}
