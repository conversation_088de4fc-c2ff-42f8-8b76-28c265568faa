import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog'

export interface ConfirmationDialogProps {
  /**
   * Whether the dialog is open
   */
  isOpen: boolean
  /**
   * Function to call when the dialog's open state changes
   */
  onOpenChange: (open: boolean) => void
  /**
   * Function to call when the user confirms the action
   */
  onConfirm: () => void
  /**
   * Title of the confirmation dialog
   */
  title?: string
  /**
   * Description text explaining the action
   */
  description?: string
  /**
   * Text for the cancel button
   */
  cancelText?: string
  /**
   * Text for the confirm button
   */
  confirmText?: string
  /**
   * CSS class for the confirm button
   */
  confirmButtonClassName?: string
  /**
   * Maximum width for the dialog content
   */
  maxWidth?: string
}

/**
 * A reusable confirmation dialog component
 */
export function ConfirmationDialog({
  isOpen,
  onOpenChange,
  onConfirm,
  title = 'Are you sure?',
  description = 'This action cannot be undone.',
  cancelText = 'Cancel',
  confirmText = 'Confirm',
  confirmButtonClassName = 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  maxWidth = 'sm:max-w-[425px]',
}: ConfirmationDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className={maxWidth}>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="mt-4">
          <AlertDialogCancel onClick={() => onOpenChange(false)}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            className={confirmButtonClassName}
            onClick={onConfirm}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
