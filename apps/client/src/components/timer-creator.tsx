import type { Schema } from '@ftt/shared'
import { useZero } from '@rocicorp/zero/react'
import * as v from 'valibot'

import type { FC } from 'react'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { useAppForm } from '~/components/ui/tanstack-form'
import { startTimer } from '~/lib/model/app-state'
import { TaskPicker } from './task-picker'

// Define Valibot schema for timer creation form
const TimerFormSchema = v.object({
  taskId: v.string('Task is required'),
})

interface TimerCreatorProps {
  isOpen: boolean
  onClose: () => void
}

export const TimerCreator: FC<TimerCreatorProps> = ({ isOpen, onClose }) => {
  const z = useZero<Schema>()

  // Create form with TanStack Form
  const form = useAppForm({
    defaultValues: {
      taskId: '',
    },
    onSubmit: async ({ value }) => {
      await startTimer(z, value.taskId)
      // Reset the form after submission
      form.reset()
      // Close the dialog after submission
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: TimerFormSchema,
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <form.AppForm>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              void form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>Create New Timer</DialogTitle>
              <DialogDescription>
                Select a task to create a new timer.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <form.AppField name="taskId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Task</field.FormLabel>
                    <field.FormControl>
                      <div className="relative">
                        <TaskPicker
                          display="search"
                          value={field.state.value}
                          onChange={(value) => {
                            field.handleChange(value)
                            field.handleBlur()
                            form.handleSubmit()
                          }}
                          onBlur={field.handleBlur}
                          className="w-full"
                        />
                      </div>
                    </field.FormControl>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>
            </div>

            <DialogFooter>
              <form.Subscribe
                selector={(state) => [state.canSubmit, state.isSubmitting]}
              >
                {([canSubmit, isSubmitting]) => (
                  <Button type="submit" disabled={!canSubmit || isSubmitting}>
                    {isSubmitting ? 'Creating...' : 'Create Timer'}
                  </Button>
                )}
              </form.Subscribe>
            </DialogFooter>
          </form>
        </form.AppForm>
      </DialogContent>
    </Dialog>
  )
}
