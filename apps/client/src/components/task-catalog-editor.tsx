import { useAppForm } from '@/components/ui/tanstack-form'
import type { TaskCatalog } from '@ftt/shared'
import { type FC, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { minLength, optional, pipe, string } from 'valibot'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { ProjectCatalogPicker } from '~/components/project-catalog-picker'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'

// Interface definitions
export interface TaskCatalogEditorProps {
  taskCatalog: TaskCatalog | null
  isOpen: boolean
  onClose: () => void
  onSave: (taskCatalog: TaskCatalog) => void
  onDelete?: (taskCatalog: TaskCatalog) => void
}

// Define Valibot schema for task catalog validation
export const TaskCatalogSchema = v.object({
  id: string(),
  name: pipe(string(), minLength(2, 'Name must be at least 2 characters')),
  key: optional(string(), ''),
  status: optional(string(), ''),
  remoteId: optional(string(), ''),
  remoteUrl: optional(string(), ''),
  projectCatalogId: optional(string(), ''),
  pinned: optional(v.boolean(), false),
})

export type TaskCatalogInput = v.InferInput<typeof TaskCatalogSchema>

export const TaskCatalogEditor: FC<TaskCatalogEditorProps> = ({
  taskCatalog,
  isOpen,
  onClose,
  onSave,
  onDelete,
}) => {
  const defaultValues: TaskCatalogInput = {
    id: taskCatalog?.id || '',
    name: taskCatalog?.name || '',
    key: taskCatalog?.key || '',
    status: taskCatalog?.status || '',
    remoteId: taskCatalog?.remoteId || '',
    remoteUrl: taskCatalog?.remoteUrl || '',
    projectCatalogId: taskCatalog?.projectCatalogId || '',
    pinned: taskCatalog?.pinned || false,
  }

  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedTaskCatalog: TaskCatalog = {
        ...taskCatalog,
        id: value.id ?? uuidv7(),
        name: value.name,
        key: value.key,
        status: value.status,
        remoteId: value.remoteId,
        remoteUrl: value.remoteUrl,
        projectCatalogId: value.projectCatalogId,
        pinned: value.pinned,
        updatedAt: Date.now(),
      } as TaskCatalog

      onSave(updatedTaskCatalog)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: TaskCatalogSchema,
    },
  })

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (taskCatalog && onDelete) {
      onDelete(taskCatalog)
    }
    setIsDeleteConfirmOpen(false)
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[500px]">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>
                {taskCatalog ? 'Edit' : 'Add'} Task Catalog
              </DialogTitle>
              <DialogDescription>
                Configure task catalog details for remote integration.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-3 py-3">
              <form.AppField name="name">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Name</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Task Catalog Name"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Task catalog name
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="key">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Key</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Task Catalog Key"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Key identifier (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="projectCatalogId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Project Catalog</field.FormLabel>
                    <field.FormControl>
                      <ProjectCatalogPicker
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Project catalog this task belongs to (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="status">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Status</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Status"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Status (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="remoteId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Remote ID</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Remote ID"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      ID from remote service (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="remoteUrl">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Remote URL</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Remote URL"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      URL to task in remote service (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="pinned">
                {(field) => (
                  <field.FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <field.FormControl>
                      <input
                        type="checkbox"
                        checked={field.state.value}
                        onChange={(e) => field.handleChange(e.target.checked)}
                        onBlur={field.handleBlur}
                        className="h-4 w-4 mt-1"
                      />
                    </field.FormControl>
                    <div className="space-y-1 leading-none">
                      <field.FormLabel>Pinned</field.FormLabel>
                      <field.FormDescription>
                        Pin this task catalog to the top of the list
                      </field.FormDescription>
                    </div>
                  </field.FormItem>
                )}
              </form.AppField>
            </div>
            <DialogFooter>
              {taskCatalog && onDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDeleteClick}
                  className="mr-auto"
                >
                  Delete
                </Button>
              )}
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Task Catalog"
        description="Are you sure you want to delete this task catalog? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </>
  )
}
