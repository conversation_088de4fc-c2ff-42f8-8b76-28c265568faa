import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import {
  type FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { cn } from '~/lib/utils'
import { Combobox, CommandOptions } from './combobox'

export interface TaskPickerProps {
  value?: string
  onChange?: (taskId: string) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  display?: 'combobox' | 'search'
}

export const TaskPicker: FC<TaskPickerProps> = ({
  value,
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  display = 'combobox',
}) => {
  const z = useZero<Schema>()
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  // Determine if we should use search query
  const shouldUseSearch = debouncedSearchQuery.length >= 2

  // Create the query based on search term
  const query = useMemo(() => {
    let q = z.query.tasks.related('project', (q) => q.related('customer'))

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or, exists }) => {
        const conditions = [
          cmp('name', 'ILIKE', searchPattern),
          exists('project', (iq) =>
            iq.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
          ),
        ]
        if (value) {
          conditions.push(cmp('id', '=', value))
        }
        return or(...conditions)
      })
    }
    q = q.orderBy('lastUsed', 'desc')

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, value])

  const [tasks = [], taskQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const taskOptions = useMemo(
    () => tasks.map((it) => ({ value: it.id, label: formatTaskLabel(it) })),
    [tasks]
  )

  console.log('render task picker', {
    value,
    numTasks: tasks.length,
  })
  const wrappedOnChange = useCallback(
    (newVal: string) => {
      console.log('task picker change', newVal)
      onChange?.(newVal)
    },
    [onChange]
  )

  return (
    <div className={cn('relative flex flex-row max-w-full min-w-0', className)}>
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={wrappedOnChange}
          onSearchTextChanged={setSearchQuery}
          options={taskOptions}
          shouldFilter={false}
          onBlur={onBlur}
          loading={taskQueryDetails.type !== 'complete'}
        />
      ) : (
        <CommandOptions
          options={taskOptions}
          value={value}
          onChange={wrappedOnChange}
          onSearchTextChanged={setSearchQuery}
          shouldFilter={false}
          onBlur={onBlur}
          loading={taskQueryDetails.type !== 'complete'}
        />
      )}
    </div>
  )
}

function formatTaskLabel(task: {
  name: string
  project?: { name?: string; customer?: { name?: string } }
}): string {
  return `${task.project?.customer?.name} - ${task.project?.name ? `${task.project.name} - ` : ''}${task.name}`
}
