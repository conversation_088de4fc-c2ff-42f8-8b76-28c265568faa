import type { Customer } from '@ftt/shared'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@radix-ui/react-select'
import { useStore } from '@tanstack/react-form'
import { type ChangeEvent, type FC, useEffect, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { TIME_NORMALIZATION_OPTIONS } from '~/lib/utils/time-normalization'
import { Combobox } from './combobox'
import { ConfirmationDialog } from './confirmation-dialog'
import { Button } from './ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'
import { Input } from './ui/input'
import { useAppForm } from './ui/tanstack-form'

// Constants
const CURRENCY_OPTIONS = [
  { value: 'EUR', label: 'EUR' },
  { value: 'USD', label: 'USD' },
  { value: 'GBP', label: 'GBP' },
]

// Define Valibot schema for customer validation
const CustomerSchema = v.object({
  id: v.string(),
  name: v.pipe(
    v.string(),
    v.minLength(2, 'Name must be at least 2 characters')
  ),
  rateValue: v.nullish(v.number()),
  rateCurrency: v.optional(v.string(), ''),
  timeNormalizationType: v.optional(v.string(), ''),
  timeNormalizationConfig: v.optional(v.string(), ''),
})

type CustomerInput = v.InferInput<typeof CustomerSchema>

// Interface definitions

interface CustomerEditorProps {
  customer: Customer | null
  isOpen: boolean
  onClose: () => void
  onSave: (customer: Customer) => void
  onDelete?: (customer: Customer) => void
}

export const CustomerEditor: FC<CustomerEditorProps> = ({
  customer,
  isOpen,
  onClose,
  onSave,
  onDelete,
}) => {
  const defaultValues: CustomerInput = {
    id: customer?.id ?? '',
    name: customer?.name ?? '',
    rateValue: customer?.rateValue,
    rateCurrency: customer?.rateCurrency ?? '',
    timeNormalizationType: customer?.timeNormalizationType ?? '',
    timeNormalizationConfig: customer?.timeNormalizationConfig ?? '',
  }
  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedCustomer: Customer = {
        ...customer,
        id: value.id ?? uuidv7(),
        name: value.name,
        rateValue: value.rateValue,
        rateCurrency: value.rateCurrency,
        timeNormalizationType: value.timeNormalizationType,
        timeNormalizationConfig: value.timeNormalizationConfig,
        updatedAt: Date.now(),
      } as Customer

      onSave(updatedCustomer)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: CustomerSchema,
    },
  })

  // Reset form when customer changes
  useEffect(() => {
    if (isOpen) {
      if (customer) {
        form.reset({
          id: customer.id,
          name: customer.name,
          rateValue: customer.rateValue,
          rateCurrency: customer.rateCurrency ?? '',
          timeNormalizationType: customer.timeNormalizationType ?? '',
          timeNormalizationConfig: customer.timeNormalizationConfig ?? '',
        })
      } else {
        form.reset({
          id: '',
          name: '',
          rateValue: undefined,
          rateCurrency: '',
          timeNormalizationType: '',
          timeNormalizationConfig: '',
        })
      }
    }
  }, [customer, isOpen, form])

  // Get form errors for potential future use
  useStore(form.store, (state) => ({
    errors: state.errors,
  }))

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (customer && onDelete) {
      onDelete(customer)
    }
    setIsDeleteConfirmOpen(false)
    onClose()
  }

  // Form components are defined above

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => !open && onClose()}
        modal={true}
      >
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>{customer ? 'Edit' : 'Add'} Customer</DialogTitle>
              <DialogDescription>
                Configure customer details and billing settings.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-3 py-3">
              <form.AppField name="name">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Name</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Customer Name"
                        value={field.state.value}
                        onChange={(e: ChangeEvent<HTMLInputElement>) =>
                          field.handleChange(e.target.value)
                        }
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>Customer name</field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <div className="grid grid-cols-2 gap-3">
                <form.AppField name="rateValue">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Rate Value</field.FormLabel>
                      <field.FormControl>
                        <Input
                          type="number"
                          placeholder="Rate Value"
                          value={field.state.value ?? ''}
                          onChange={(e: ChangeEvent<HTMLInputElement>) => {
                            const value = e.target.valueAsNumber
                            field.handleChange(value)
                          }}
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>Hourly rate</field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="rateCurrency">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Currency</field.FormLabel>
                      <field.FormControl>
                        <Select
                          value={field.state.value}
                          onValueChange={field.handleChange}
                          onOpenChange={() => field.handleBlur()}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select Currency" />
                          </SelectTrigger>
                          <SelectContent>
                            {CURRENCY_OPTIONS.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </field.FormControl>
                      <field.FormDescription>
                        Rate currency
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <form.AppField name="timeNormalizationType">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Time Normalization</field.FormLabel>
                      <field.FormControl>
                        <Combobox
                          value={field.state.value}
                          onChange={field.handleChange}
                          onBlur={field.handleBlur}
                          options={TIME_NORMALIZATION_OPTIONS}
                          hint="Select normalization type"
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        Time normalization method
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="timeNormalizationConfig">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Config Value</field.FormLabel>
                      <field.FormControl>
                        <Input
                          placeholder="e.g., 15"
                          value={field.state.value}
                          onChange={(e: ChangeEvent<HTMLInputElement>) =>
                            field.handleChange(e.target.value)
                          }
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        E.g., "15" for 15-minute increments
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>
              </div>
            </div>
            <DialogFooter className="gap-2">
              <div className="flex gap-2 justify-between w-full">
                <div>
                  {customer && onDelete && (
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={handleDeleteClick}
                    >
                      Delete
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button type="submit">Save</Button>
                </div>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteConfirm}
        title="Are you sure?"
        description={`This will permanently delete the customer "${customer?.name}". This action cannot be undone.`}
        confirmText="Delete"
      />
    </>
  )
}
