import { type FC, useEffect } from 'react'
import { useRefify } from '~/lib/hooks/use-refify'
import { supportsInvoke } from '~/lib/idle-time'
import { isWindowFocused } from '~/lib/window-state'

interface ForegroundCheckerProps {
  onForeground: () => void
  enabled: boolean
}

export const ForegroundChecker: FC<ForegroundCheckerProps> = ({
  onForeground,
  enabled,
}) => {
  const onForegroundRef = useRefify(onForeground)
  const enabledRef = useRefify(enabled)

  useEffect(() => {
    if (!supportsInvoke()) {
      return
    }
    async function checkForeground() {
      const isForeground = await isWindowFocused()
      if (isForeground && enabledRef.current) {
        onForegroundRef.current()
      }
    }
    const timeoutId = setInterval(() => {
      checkForeground()
    }, 10000)

    return () => clearInterval(timeoutId)
  }, [onForegroundRef, enabledRef])
  return null
}
