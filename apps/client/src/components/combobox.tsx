import { Check, ChevronsUpDown } from 'lucide-react'
import { type FC, useState } from 'react'

import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { CommandLoading } from 'cmdk'

export interface ComboboxOption {
  value: string
  label: string
}

export interface CommandOptionsProps {
  options: ComboboxOption[]
  value?: string
  onChange?: (value: string) => void
  onSearchTextChanged?: (value: string) => void
  searchPlaceholder?: string
  emptyMsg?: string
  loading?: boolean
  shouldFilter?: boolean
  onBlur?: () => void
}

export const CommandOptions: FC<CommandOptionsProps> = ({
  options,
  value,
  onChange,
  onSearchTextChanged,
  searchPlaceholder = 'Search option...',
  emptyMsg = 'No matching option found',
  loading = false,
  shouldFilter = true,
  onBlur,
}) => {
  return (
    <Command shouldFilter={shouldFilter} onBlur={onBlur}>
      <CommandInput
        placeholder={searchPlaceholder}
        onValueChange={onSearchTextChanged}
      />
      <CommandList>
        <CommandEmpty>{emptyMsg}</CommandEmpty>
        {loading && <CommandLoading />}
        <CommandGroup>
          {options.map((option) => (
            <CommandItem
              key={option.value}
              value={option.value}
              onSelect={(currentValue) => {
                onChange?.(currentValue)
              }}
            >
              <Check
                className={cn(
                  'mr-2 h-4 w-4',
                  value === option.value ? 'opacity-100' : 'opacity-0'
                )}
              />
              {option.label}
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  )
}

export interface ComboboxProps {
  className?: string
  options?: ComboboxOption[]
  onChange?: (value: string) => void
  value?: string
  hint?: string
  searchPlaceholder?: string
  onBlur?: () => void
  onSearchTextChanged?: (value: string) => void
  emptyMsg?: string
  loading?: boolean
  shouldFilter?: boolean
}

export const Combobox: FC<ComboboxProps> = ({
  options = [],
  value,
  onChange,
  onBlur,
  onSearchTextChanged,
  hint = 'Select option',
  loading = false,
  searchPlaceholder = 'Search option...',
  emptyMsg = 'No matching option found',
  shouldFilter = true,
}) => {
  const [open, setOpen] = useState(false)

  const handleSelect = (selectedValue: string) => {
    setOpen(false)
    onChange?.(selectedValue)
  }

  const triggerButtonLabel = value
    ? options.find((option) => option.value === value)?.label
    : hint
  return (
    <Popover open={open} onOpenChange={setOpen} modal={true}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          // biome-ignore lint/a11y/useSemanticElements: <explanation>
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between flex flex-row overflow-hidden shrink min-w-0"
          onBlur={onBlur}
        >
          <span className="grow truncate">{triggerButtonLabel}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)] max-w-[var(--radix-popover-trigger-width)]">
        <CommandOptions
          options={options}
          value={value}
          onChange={handleSelect}
          onSearchTextChanged={onSearchTextChanged}
          searchPlaceholder={searchPlaceholder}
          emptyMsg={emptyMsg}
          loading={loading}
          shouldFilter={shouldFilter}
        />
      </PopoverContent>
    </Popover>
  )
}
