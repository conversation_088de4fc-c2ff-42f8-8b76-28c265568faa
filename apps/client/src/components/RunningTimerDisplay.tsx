import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { PlayIcon, StopCircleIcon as StopIcon } from 'lucide-react'
import { type FC, useEffect, useState } from 'react'

import { Button } from '~/components/ui/button'
import { canStopTimerFromDb, stopTimer } from '~/lib/model/app-state'

export const RunningTimerDisplay: FC = () => {
  const z = useZero<Schema>()
  const [appState, appStateDetails] = useQuery(
    z.query.appState.where('id', '=', z.userID).related('runningTimer').one(),
    {
      ttl: 'forever',
    }
  )

  const [elapsedTime, setElapsedTime] = useState<string>('00:00:00')
  const stopEnabled =
    appStateDetails.type === 'complete' &&
    appState &&
    canStopTimerFromDb(appState)
  const runningTimerId =
    appStateDetails.type === 'complete' ? appState?.runningTimerId : undefined

  // Query for running timers with their associated timerecords
  const [runningTimers = []] = useQuery(
    z.query.timers
      .where('status', '=', 'running')
      .related('timerecord', (q) =>
        q.related('task', (q) => q.related('project'))
      )
      .limit(1),
    {
      ttl: 0, // No caching, always get the latest
    }
  )

  const curRunningTimer = runningTimers[0]

  // Update elapsed time every second
  useEffect(() => {
    if (!curRunningTimer) {
      return
    }

    const updateElapsedTime = () => {
      const startTime = curRunningTimer.startTimestamp
      const now = Date.now()
      const elapsedMs = now - startTime

      // Format elapsed time
      const seconds = Math.floor((elapsedMs / 1000) % 60)
      const minutes = Math.floor((elapsedMs / (1000 * 60)) % 60)
      const hours = Math.floor(elapsedMs / (1000 * 60 * 60))

      setElapsedTime(
        `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      )
    }

    // Update immediately
    updateElapsedTime()

    // Then update every second
    const intervalId = setInterval(updateElapsedTime, 1000)

    return () => clearInterval(intervalId)
  }, [curRunningTimer])

  // Handle stopping the timer
  const handleStopTimer = () => {
    if (runningTimerId) {
      console.log('stopping timer:', runningTimerId)
      stopTimer(z, runningTimerId, Date.now()).catch((error) => {
        console.error('Error stopping timer:', error)
      })
    }
  }

  // If no running timer or timerecord, don't render anything
  if (!curRunningTimer || !curRunningTimer.timerecord) {
    return null
  }

  // Get task and project info
  const task = curRunningTimer.timerecord.task
  const projectName = task?.project?.name
  const taskName = task?.name

  return (
    <div
      className="flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-md"
      title="Press ⌘+E to edit this time record"
    >
      <PlayIcon className="h-4 w-4 text-primary" />
      <div className="flex flex-col">
        <div className="text-xs text-muted-foreground">
          Running Timer (⌘+E to edit)
        </div>
        <div className="text-sm font-medium">
          {projectName ? `${projectName} - ` : ''}
          {taskName}
        </div>
      </div>
      <div className="text-sm font-mono">{elapsedTime}</div>
      <Button
        variant="outline"
        size="sm"
        className="ml-2 h-7 px-2"
        disabled={!stopEnabled}
        onClick={handleStopTimer}
        title="Press ⌘+O to stop timer"
      >
        <StopIcon className="h-4 w-4 mr-1" />
        Stop
      </Button>
    </div>
  )
}
