import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { type FC, useEffect, useState } from 'react'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './ui/command'

interface TimerLauncherProps {
  onClosed: () => void
}
export const TimerLauncher: FC<TimerLauncherProps> = ({ onClosed }) => {
  const z = useZero<Schema>()

  // Fetch all tasks for the dropdown
  const [tasks = []] = useQuery(
    z.query.tasks.related('project').orderBy('lastUsed', 'desc').limit(100),
    {
      ttl: 'forever',
    }
  )
  const [open, setOpen] = useState(false)

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }
    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  return (
    <CommandDialog
      open={open}
      onOpenChange={(e) => {
        if (!e) {
          onClosed()
        }
        setOpen(e)
      }}
    >
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Tasks">
          {tasks.map((task) => (
            <CommandItem key={task.id}>{task.name}</CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  )
}
