import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { type FC, useEffect, useMemo, useRef, useState } from 'react'

import { cn } from '~/lib/utils'
import { Combobox, CommandOptions } from './combobox'

export interface ProjectCatalogPickerProps {
  value?: string
  onChange?: (projectCatalogId: string) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  display?: 'combobox' | 'search'
}

export const ProjectCatalogPicker: FC<ProjectCatalogPickerProps> = ({
  value,
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  display = 'combobox',
}) => {
  const z = useZero<Schema>()
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  const shouldUseSearch = debouncedSearchQuery.length >= 2

  const query = useMemo(() => {
    let q = z.query.projectCatalogs

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or }) => {
        const conditions = [
          cmp('name', 'ILIKE', searchPattern),
          cmp('key', 'ILIKE', searchPattern),
        ]
        if (value) {
          conditions.push(cmp('id', '=', value))
        }
        return or(...conditions)
      })
    } else {
      q = q.orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, value])

  const [projectCatalogs = [], projectCatalogQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const projectCatalogOptions = useMemo(
    () =>
      projectCatalogs.map((it) => ({
        value: it.id,
        label: formatProjectCatalogLabel(it),
      })),
    [projectCatalogs]
  )

  return (
    <div className={cn('relative', className)}>
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          options={projectCatalogOptions}
          shouldFilter={false}
          onBlur={onBlur}
          loading={projectCatalogQueryDetails.type !== 'complete'}
        />
      ) : (
        <CommandOptions
          options={projectCatalogOptions}
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          shouldFilter={false}
          onBlur={onBlur}
          loading={projectCatalogQueryDetails.type !== 'complete'}
        />
      )}
    </div>
  )
}

function formatProjectCatalogLabel(projectCatalog: {
  name: string
  key?: string | null
}): string {
  return projectCatalog.key
    ? `${projectCatalog.name} (${projectCatalog.key})`
    : projectCatalog.name
}
