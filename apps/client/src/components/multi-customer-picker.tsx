import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { CommandLoading } from 'cmdk'
import { X } from 'lucide-react'
import { type FC, useEffect, useMemo, useRef, useState } from 'react'
import { cn } from '~/lib/utils'

export interface ComboboxOption {
  value: string
  label: string
}

export interface MultiCustomerPickerProps {
  values?: string[]
  onChange?: (values: string[]) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  placeholder?: string
  allowClear?: boolean
}

export const MultiCustomerPicker: FC<MultiCustomerPickerProps> = ({
  values = [],
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  placeholder = 'Select customers',
  allowClear = false,
}) => {
  const z = useZero<Schema>()
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  const shouldUseSearch = debouncedSearchQuery.length >= 2

  const query = useMemo(() => {
    let q = z.query.customers

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or }) => {
        const conditions = [cmp('name', 'ILIKE', searchPattern)]
        if (values.length > 0) {
          conditions.push(cmp('id', 'IN', values))
        }
        return or(...conditions)
      })
    } else {
      q = q.orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, values])

  const [customers = [], customerQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const customerOptions = useMemo(
    () =>
      customers.map((it) => ({ value: it.id, label: formatCustomerLabel(it) })),
    [customers]
  )

  // Get selected customer labels
  const selectedCustomers = useMemo(() => {
    return values
      .map((value) => {
        const option = customerOptions.find((opt) => opt.value === value)
        return option ? option.label : null
      })
      .filter(Boolean) as string[]
  }, [values, customerOptions])

  const handleSelect = (selectedValue: string) => {
    if (values.includes(selectedValue)) {
      // Remove if already selected
      onChange?.(values.filter((value) => value !== selectedValue))
    } else {
      // Add if not selected
      onChange?.([...values, selectedValue])
    }
  }

  const handleRemove = (valueToRemove: string) => {
    onChange?.(values.filter((value) => value !== valueToRemove))
  }

  const handleClear = () => {
    onChange?.([])
  }

  return (
    <div
      className={cn('w-full relative flex flex-row items-center', className)}
    >
      <Popover open={open} onOpenChange={setOpen} modal={true}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            // biome-ignore lint/a11y/useSemanticElements: <explanation>
            role="combobox"
            aria-expanded={open}
            className="justify-between h-fit flex flex-row align-middle shrink flex-wrap"
            onBlur={onBlur}
          >
            <div className="flex flex-wrap gap-1 items-center">
              {selectedCustomers.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {selectedCustomers.map((label, index) => (
                    <Badge
                      key={values[index]}
                      variant="secondary"
                      className="mr-1"
                    >
                      {label}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemove(values[index])
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)] max-w-[var(--radix-popover-trigger-width)]">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Search customers..."
              onValueChange={setSearchQuery}
              ref={commandInputRef}
            />
            <CommandList>
              <CommandEmpty>No matching customers found</CommandEmpty>
              {customerQueryDetails.type !== 'complete' && <CommandLoading />}
              <CommandGroup>
                {customerOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={handleSelect}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        values.includes(option.value)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible'
                      )}
                    >
                      <svg
                        className="h-3 w-3"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <title>Clear</title>
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {allowClear && values.length > 0 && (
        <Button variant="ghost" size="sm" className="" onClick={handleClear}>
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}

function formatCustomerLabel(customer: {
  name: string
}): string {
  return customer.name
}
