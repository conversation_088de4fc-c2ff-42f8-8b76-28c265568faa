import { useEffect, useState } from 'react'
import { getIdleTime, hasBeenIdleFor, supportsInvoke } from '~/lib/idle-time'
import { Button } from './ui/button'

/**
 * Fetch idle time and update state
 */
async function fetchIdleTime(
  setIdleTime: (time: number | null) => void,
  setIsIdleForOneMinute: (idle: boolean | null) => void,
  setError: (error: string | null) => void,
  setIsPolling: (isPolling: boolean) => void
) {
  try {
    console.log('getting idle time')
    const time = await getIdleTime()
    console.log('got idle time', time)
    setIdleTime(time)

    const idle = await hasBeenIdleFor(60000) // 1 minute
    setIsIdleForOneMinute(idle)

    setError(null)
  } catch (err) {
    setError(err instanceof Error ? err.message : String(err))
    setIsPolling(false)
  }
}

const invokeSupported = supportsInvoke()

export function IdleTimeDisplay() {
  const [idleTime, setIdleTime] = useState<number | null>(null)
  const [isIdleForOneMinute, setIsIdleForOneMinute] = useState<boolean | null>(
    null
  )
  const [error, setError] = useState<string | null>(null)
  const [isPolling, setIsPolling] = useState(false)

  const togglePolling = () => {
    setIsPolling((prev) => !prev)
  }

  useEffect(() => {
    if (!isPolling) return

    const interval = setInterval(
      () =>
        fetchIdleTime(
          setIdleTime,
          setIsIdleForOneMinute,
          setError,
          setIsPolling
        ),
      1000
    )
    return () => clearInterval(interval)
  }, [isPolling])

  return (
    <div className="p-4 border rounded-lg shadow-sm">
      <h3 className="text-lg font-medium mb-2">System Idle Time</h3>

      {error ? (
        <div className="text-red-500 mb-2">Error: {error}</div>
      ) : (
        <>
          <div className="mb-2">
            <span className="font-medium">Current idle time: </span>
            {idleTime !== null
              ? `${(idleTime / 1000).toFixed(1)} seconds`
              : 'Loading...'}
          </div>

          <div className="mb-4">
            <span className="font-medium">Idle for more than 1 minute: </span>
            {isIdleForOneMinute !== null
              ? isIdleForOneMinute
                ? 'Yes'
                : 'No'
              : 'Loading...'}
          </div>
        </>
      )}

      <div className="flex gap-2">
        <Button
          disabled={!invokeSupported}
          onClick={() =>
            fetchIdleTime(
              setIdleTime,
              setIsIdleForOneMinute,
              setError,
              setIsPolling
            )
          }
          variant="outline"
        >
          Check Now
        </Button>

        <Button
          disabled={!invokeSupported}
          onClick={togglePolling}
          variant={isPolling ? 'destructive' : 'default'}
        >
          {isPolling ? 'Stop Polling' : 'Start Polling'}
        </Button>
      </div>
    </div>
  )
}
