import type { Project } from '@ftt/shared'
import { type FC, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { TIME_NORMALIZATION_OPTIONS } from '~/lib/utils/time-normalization'
import { Combobox } from './combobox'
import { ConfirmationDialog } from './confirmation-dialog'
import { CustomerPicker } from './customer-picker'
import { Button } from './ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'
import { Input } from './ui/input'
import { useAppForm } from './ui/tanstack-form'

// Define Valibot schema for project validation
const ProjectSchema = v.object({
  id: v.string(),
  name: v.pipe(
    v.string(),
    v.minLength(2, 'Name must be at least 2 characters')
  ),
  customerId: v.string('Customer is required'),
  color: v.optional(v.string(), ''),
  timeNormalizationType: v.optional(v.string(), ''),
  timeNormalizationConfig: v.optional(v.string(), ''),
})

type ProjectInput = v.InferInput<typeof ProjectSchema>

interface ProjectEditorProps {
  project: Project | null
  isOpen: boolean
  onClose: () => void
  onSave: (project: Project) => void
  onDelete?: (project: Project) => void
}

export const ProjectEditor: FC<ProjectEditorProps> = ({
  project,
  isOpen,
  onClose,
  onSave,
  onDelete,
}) => {
  const defaultValues: ProjectInput = {
    id: project?.id ?? '',
    name: project?.name ?? '',
    customerId: project?.customerId ?? '',
    color: project?.color ?? '',
    timeNormalizationType: project?.timeNormalizationType ?? '',
    timeNormalizationConfig: project?.timeNormalizationConfig ?? '',
  }

  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedProject: Project = {
        ...project,
        id: value.id ?? uuidv7(),
        name: value.name,
        customerId: value.customerId,
        color: value.color,
        timeNormalizationType: value.timeNormalizationType,
        timeNormalizationConfig: value.timeNormalizationConfig,
        updatedAt: Date.now(),
      } as Project

      onSave(updatedProject)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: ProjectSchema,
    },
  })

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (project && onDelete) {
      onDelete(project)
    }
    setIsDeleteConfirmOpen(false)
    onClose()
  }

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => !open && onClose()}
        modal={true}
      >
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>{project ? 'Edit' : 'Add'} Project</DialogTitle>
              <DialogDescription>
                Configure project details and settings.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-3 py-3">
              <form.AppField name="name">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Name</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Project Name"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>Project name</field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="customerId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Customer</field.FormLabel>
                    <field.FormControl>
                      <CustomerPicker
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Customer this project belongs to
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="color">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Color</field.FormLabel>
                    <field.FormControl>
                      <div className="flex gap-2 items-center">
                        <Input
                          type="color"
                          className="w-12 h-8 p-1"
                          value={field.state.value ?? '#000000'}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                        />
                        <Input
                          placeholder="#RRGGBB"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                          className="flex-1"
                        />
                      </div>
                    </field.FormControl>
                    <field.FormDescription>
                      Color for the project (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="timeNormalizationType">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Time Normalization</field.FormLabel>
                    <field.FormControl>
                      <Combobox
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onBlur={field.handleBlur}
                        options={TIME_NORMALIZATION_OPTIONS}
                        hint="Select normalization type"
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      How time should be normalized (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="timeNormalizationConfig">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Normalization Config</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Configuration"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Configuration for time normalization (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>
            </div>
            <DialogFooter className="gap-2 mt-4">
              {project && onDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDeleteClick}
                >
                  Delete
                </Button>
              )}
              <div className="flex-1" />
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Project"
        description="Are you sure you want to delete this project? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </>
  )
}
