import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { type FC, useEffect, useMemo, useRef, useState } from 'react'

import { cn } from '~/lib/utils'
import { Combobox, CommandOptions } from './combobox'

export interface ProjectPickerProps {
  value?: string
  onChange?: (taskId: string) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  display?: 'combobox' | 'search'
}

export const ProjectPicker: FC<ProjectPickerProps> = ({
  value,
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  display = 'combobox',
}) => {
  const z = useZero<Schema>()
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  // Determine if we should use search query
  const shouldUseSearch = debouncedSearchQuery.length >= 2

  // Create the query based on search term
  const query = useMemo(() => {
    let q = z.query.projects.related('customer')

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or, exists }) => {
        const conditions = [
          cmp('name', 'ILIKE', searchPattern),
          exists('customer', (iq) =>
            iq.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
          ),
        ]
        if (value) {
          conditions.push(cmp('id', '=', value))
        }
        return or(...conditions)
      })
    } else {
      q = q.orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, value])

  const [projects = [], projectQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const projectOptions = useMemo(
    () =>
      projects.map((it) => ({ value: it.id, label: formatProjectLabel(it) })),
    [projects]
  )

  return (
    <div className={cn('relative', className)}>
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          options={projectOptions}
          shouldFilter={false}
          onBlur={onBlur}
          loading={projectQueryDetails.type !== 'complete'}
        />
      ) : (
        <CommandOptions
          options={projectOptions}
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          shouldFilter={false}
          onBlur={onBlur}
          loading={projectQueryDetails.type !== 'complete'}
        />
      )}
    </div>
  )
}

function formatProjectLabel(project: {
  name: string
  customer?: { name?: string }
}): string {
  return `${project.customer?.name ? `${project.customer.name} - ` : ''}${project.name}`
}
