import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { type FC, useEffect, useMemo, useRef, useState } from 'react'

import { cn } from '~/lib/utils'
import { Combobox, CommandOptions } from './combobox'

export interface TaskCatalogPickerProps {
  value?: string
  onChange?: (taskCatalogId: string) => void
  onBlur?: () => void
  className?: string
  limit?: number
  autoFocus?: boolean
  display?: 'combobox' | 'search'
  projectCatalogId?: string
}

export const TaskCatalogPicker: FC<TaskCatalogPickerProps> = ({
  value,
  onChange,
  onBlur,
  className,
  limit = 100,
  autoFocus = false,
  display = 'combobox',
  projectCatalogId,
}) => {
  const z = useZero<Schema>()
  const [searchQuery, setSearchQuery] = useState('')
  const commandInputRef = useRef<HTMLInputElement>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the input when autoFocus is true
  useEffect(() => {
    if (autoFocus && commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [autoFocus])

  const shouldUseSearch = debouncedSearchQuery.length >= 2

  const query = useMemo(() => {
    let q = z.query.taskCatalogs.related('projectCatalog')

    if (projectCatalogId) {
      q = q.where('projectCatalogId', '=', projectCatalogId)
    }

    if (shouldUseSearch) {
      const searchPattern = `%${debouncedSearchQuery}%`

      q = q.where(({ cmp, or, exists }) => {
        const conditions = [
          cmp('name', 'ILIKE', searchPattern),
          cmp('key', 'ILIKE', searchPattern),
          exists('projectCatalog', (iq) =>
            iq.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
          ),
        ]
        if (value) {
          conditions.push(cmp('id', '=', value))
        }
        return or(...conditions)
      })
    } else {
      // If no search, order by last used or name
      q = q.orderBy('lastUsed', 'desc').orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, limit, value, projectCatalogId])

  const [taskCatalogs = [], taskCatalogQueryDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const taskCatalogOptions = useMemo(
    () =>
      taskCatalogs.map((it) => ({
        value: it.id,
        label: formatTaskCatalogLabel(it),
      })),
    [taskCatalogs]
  )

  return (
    <div className={cn('relative', className)}>
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          options={taskCatalogOptions}
          shouldFilter={false}
          onBlur={onBlur}
          loading={taskCatalogQueryDetails.type !== 'complete'}
        />
      ) : (
        <CommandOptions
          options={taskCatalogOptions}
          value={value}
          onChange={onChange}
          onSearchTextChanged={setSearchQuery}
          shouldFilter={false}
          onBlur={onBlur}
          loading={taskCatalogQueryDetails.type !== 'complete'}
        />
      )}
    </div>
  )
}

function formatTaskCatalogLabel(taskCatalog: {
  name: string
  key?: string | null
  projectCatalog?: { name: string } | null
}): string {
  const projectName = taskCatalog.projectCatalog?.name
    ? `${taskCatalog.projectCatalog.name} - `
    : ''
  const keyPart = taskCatalog.key ? ` (${taskCatalog.key})` : ''

  return `${projectName}${taskCatalog.name}${keyPart}`
}
