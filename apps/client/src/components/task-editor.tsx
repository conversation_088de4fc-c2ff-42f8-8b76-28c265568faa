import { useAppForm } from '@/components/ui/tanstack-form'
import type { Schema, Task } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useDebounce } from '@uidotdev/usehooks'
import { type FC, useMemo, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { minLength, optional, pipe, string } from 'valibot'
import { Combobox } from '~/components/combobox'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

// Interface definitions
export interface TaskEditorProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
  onSave: (task: Task) => void
  onDelete?: (task: Task) => void
  taskCatalogId?: string
  projectId?: string
}

// Define Valibot schema for task validation
export const TaskSchema = v.object({
  id: string(),
  name: pipe(string(), minLength(2, 'Name must be at least 2 characters')),
  projectId: string('Project is required'),
  status: string('Status is required'),
  defaultTask: optional(v.boolean(), false),
  pinned: optional(v.boolean(), false),
})

export type TaskInput = v.InferInput<typeof TaskSchema>

const limit = 100

export const TaskEditor: FC<TaskEditorProps> = ({
  task,
  isOpen,
  onClose,
  onSave,
  onDelete,
  taskCatalogId,
  projectId,
}) => {
  const z = useZero<Schema>()

  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Determine if we should use search query
  const shouldUseSearch = debouncedSearchQuery.length >= 2
  const selectedProjectId = task?.projectId ?? projectId ?? undefined

  // Create the query based on search term
  const query = useMemo(() => {
    let q = z.query.projects.related('customer')

    if (shouldUseSearch) {
      // When searching, use ilike operator for case-insensitive pattern matching
      // This performs server-side filtering which is crucial for finding all possible tasks
      const searchPattern = `%${debouncedSearchQuery}%`

      // Use ilike operator for case-insensitive pattern matching
      // This performs server-side filtering which is crucial for finding all possible tasks
      // For now, we'll just search by task name since the relationship filtering is complex
      q = q.where(({ cmp, or, exists }) => {
        const conditions = [
          cmp('name', 'ILIKE', searchPattern),
          exists('customer', (iq) =>
            iq.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
          ),
        ]
        if (selectedProjectId) {
          conditions.push(cmp('id', '=', selectedProjectId))
        }
        return or(...conditions)
      })
    } else {
      q = q.orderBy('name', 'asc')
    }

    return q.limit(limit)
  }, [z, debouncedSearchQuery, shouldUseSearch, selectedProjectId])

  // Fetch all projects for the dropdown
  const [projects = [], projectDetails] = useQuery(query, {
    ttl: shouldUseSearch ? 0 : 'forever', // Don't cache search results
  })

  const defaultValues: TaskInput = {
    id: task?.id || '',
    name: task?.name || '',
    projectId: task?.projectId || '',
    status: task?.status || 'OPEN',
    defaultTask: task?.defaultTask || false,
    pinned: task?.pinned || false,
  }

  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedTask: Task = {
        ...task,
        id: value.id ?? uuidv7(),
        name: value.name,
        projectId: value.projectId,
        status: value.status,
        defaultTask: value.defaultTask,
        pinned: value.pinned,
        updatedAt: Date.now(),
      } as Task

      onSave(updatedTask)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: TaskSchema,
    },
  })

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (task && onDelete) {
      onDelete(task)
    }
    setIsDeleteConfirmOpen(false)
    onClose()
  }

  const projectOptions = useMemo(
    () =>
      projects.map((project) => {
        const customerPart = project.customer?.name
          ? `(${project.customer.name})`
          : ''
        return {
          value: project.id,
          label: `${project.name} ${customerPart}`,
        }
      }),
    [projects]
  )

  return (
    <>
      <Dialog open={isOpen} modal={true}>
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>{task ? 'Edit' : 'Add'} Task</DialogTitle>
              <DialogDescription>
                {task
                  ? 'Edit task details'
                  : taskCatalogId
                    ? 'Create a new task linked to task catalog'
                    : 'Configure task details and settings.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-3 py-3">
              <form.AppField name="name">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Name</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Task Name"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>Task name</field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="projectId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Project</field.FormLabel>
                    <field.FormControl>
                      <Combobox
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onSearchTextChanged={setSearchQuery}
                        options={projectOptions}
                        shouldFilter={false}
                        onBlur={field.handleBlur}
                        loading={projectDetails.type !== 'complete'}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Project this task belongs to
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="status">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Status</field.FormLabel>
                    <field.FormControl>
                      <Select
                        value={field.state.value}
                        onValueChange={(value) => field.handleChange(value)}
                        onOpenChange={() => field.handleBlur()}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="OPEN">Open</SelectItem>
                          <SelectItem value="CLOSED">Closed</SelectItem>
                          <SelectItem value="CHARGED">Charged</SelectItem>
                        </SelectContent>
                      </Select>
                    </field.FormControl>
                    <field.FormDescription>
                      Current status of the task
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="defaultTask">
                {(field) => (
                  <field.FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <field.FormControl>
                      <input
                        type="checkbox"
                        className="h-4 w-4 mt-1"
                        checked={field.state.value}
                        onChange={(e) => field.handleChange(e.target.checked)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <div className="space-y-1 leading-none">
                      <field.FormLabel>Default Task</field.FormLabel>
                      <field.FormDescription>
                        Set as a default task for the project
                      </field.FormDescription>
                    </div>
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="pinned">
                {(field) => (
                  <field.FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <field.FormControl>
                      <input
                        type="checkbox"
                        className="h-4 w-4 mt-1"
                        checked={field.state.value}
                        onChange={(e) => field.handleChange(e.target.checked)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <div className="space-y-1 leading-none">
                      <field.FormLabel>Pinned</field.FormLabel>
                      <field.FormDescription>
                        Pin this task to make it appear at the top of lists
                      </field.FormDescription>
                    </div>
                  </field.FormItem>
                )}
              </form.AppField>
            </div>
            <DialogFooter className="gap-2 mt-4">
              {task && onDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDeleteClick}
                >
                  Delete
                </Button>
              )}
              <div className="flex-1" />
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {isDeleteConfirmOpen && (
        <ConfirmationDialog
          isOpen={isDeleteConfirmOpen}
          onOpenChange={setIsDeleteConfirmOpen}
          onConfirm={handleDeleteConfirm}
          title="Delete Task"
          description="Are you sure you want to delete this task? This action cannot be undone."
          confirmText="Delete"
          cancelText="Cancel"
        />
      )}
    </>
  )
}
