import type { FC } from 'react'
import type { TimerStruct } from '~/lib/model/app-state'
import { CustomersTable } from '~/modules/customers/components/customers-table'
import { ProjectsTable } from '~/modules/customers/components/projects-table'
import { TasksTable } from '~/modules/customers/components/tasks-table'
import type { TimeRecord } from './time-record-editor'

interface TimeRecordsAggregateInfoProps {
  timeRecords: TimeRecord[]
  runningTimer?: TimerStruct
  durationOfRunningTimeRecord?: number
  selectedCustomerId?: string
  selectedProjectId?: string
  selectedTaskId?: string
  onCustomerChange: (customerId: string | undefined) => void
  onProjectChange: (projectId: string | undefined) => void
  onTaskChange: (taskId: string | undefined) => void
}

export const TimeRecordsAggregateInfo: FC<TimeRecordsAggregateInfoProps> = ({
  timeRecords,
  durationOfRunningTimeRecord,
  selectedCustomerId,
  selectedProjectId,
  selectedTaskId,
  runningTimer,
  onCustomerChange,
  onProjectChange,
  onTaskChange,
}) => {
  return (
    <div className="w-full flex flex-row gap-2">
      <CustomersTable
        timerecords={(timeRecords ?? []) as TimeRecord[]}
        durationOfRunningTimeRecord={durationOfRunningTimeRecord}
        selectedCustomerId={selectedCustomerId}
        runningTimeRecordId={runningTimer?.worklogId}
        onCustomerChange={onCustomerChange}
      />
      <ProjectsTable
        timerecords={(timeRecords ?? []) as TimeRecord[]}
        durationOfRunningTimeRecord={durationOfRunningTimeRecord}
        selectedCustomerId={selectedCustomerId}
        selectedProjectId={selectedProjectId}
        runningTimeRecordId={runningTimer?.worklogId}
        onProjectChange={onProjectChange}
      />
      <TasksTable
        timerecords={(timeRecords ?? []) as TimeRecord[]}
        durationOfRunningTimeRecord={durationOfRunningTimeRecord}
        selectedCustomerId={selectedCustomerId}
        selectedProjectId={selectedProjectId}
        selectedTaskId={selectedTaskId}
        runningTimeRecordId={runningTimer?.worklogId}
        onTaskChange={onTaskChange}
      />
    </div>
  )
}
