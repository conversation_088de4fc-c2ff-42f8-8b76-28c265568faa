import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import { type FC, useEffect, useMemo, useState } from 'react'
import {
  resumeTimer,
  stopAndRestartTimer,
  stopTimer,
} from '~/lib/model/app-state'
import { formatDateTime } from '~/utils/date'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog'
import { Button } from './ui/button'

export interface IdleTimeoutDialogProps {
  timestampOfIdleTimeStart: number
  runningTimerId: string
  runningTimerDescription: string | undefined
  z: Zero<Schema>
}

/**
 * Dialog that appears when the user has been idle for more than 1 minute
 * while a timer is running.
 */
export const IdleTimeoutDialog: FC<IdleTimeoutDialogProps> = ({
  timestampOfIdleTimeStart,
  runningTimerId,
  runningTimerDescription,
  z,
}) => {
  const [currentTime, setCurrentTime] = useState(Date.now())

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentTime(Date.now())
    }, 10000)

    return () => clearInterval(intervalId)
  }, [])

  // Calculate idle duration in minutes
  const idleDurationMinutes = useMemo(() => {
    return Math.floor((currentTime - timestampOfIdleTimeStart) / 60000)
  }, [currentTime, timestampOfIdleTimeStart])

  const idleStartDateTime = useMemo(() => {
    return formatDateTime(new Date(timestampOfIdleTimeStart))
  }, [timestampOfIdleTimeStart])

  // Handle continuing the timer
  const handleContinue = async () => {
    invariant(runningTimerId, 'No running timer')
    await resumeTimer(z, runningTimerId)
  }

  // Handle stopping the timer
  const handleStop = async () => {
    if (runningTimerId) {
      await stopTimer(z, runningTimerId, timestampOfIdleTimeStart)
    }
  }

  // Handle stopping and restarting the timer
  const handleStopAndRestart = async () => {
    if (runningTimerId) {
      await stopAndRestartTimer(z, runningTimerId, timestampOfIdleTimeStart)
    }
  }

  return (
    <AlertDialog open={true}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Idle Time Detected</AlertDialogTitle>
          <AlertDialogDescription>
            You have been idle for {idleDurationMinutes} minute
            {idleDurationMinutes !== 1 ? 's' : ''} (since {idleStartDateTime}).
            <br />
            <br />
            {runningTimerDescription}
            <br />
            <br />
            Do you want to continue tracking time, stop the timer, or stop and
            start a new timer?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-2">
          <AlertDialogCancel onClick={handleContinue}>
            Continue Timer
          </AlertDialogCancel>
          <Button
            variant="outline"
            onClick={handleStopAndRestart}
            className="sm:ml-2"
          >
            Stop & Restart Timer
          </Button>
          <AlertDialogAction onClick={handleStop}>Stop Timer</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
