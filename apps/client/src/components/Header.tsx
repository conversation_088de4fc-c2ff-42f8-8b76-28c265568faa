import { Link, useMatches } from '@tanstack/react-router'
import type { FC } from 'react'
import { RunningTimerDisplay } from '~/components/RunningTimerDisplay'
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from '~/components/ui/navigation-menu'
import { ModeToggle } from './mode-toggle'

export default function Header() {
  const matches = useMatches()
  const currentPath =
    matches.length > 0 ? matches[matches.length - 1].pathname : '/'

  // Get the page title based on the current path
  const getPageTitle = () => {
    const pathSegments = currentPath.split('/')
    const lastSegment = pathSegments[pathSegments.length - 1]

    // Handle root path
    if (currentPath === '/') return 'Home'

    // Handle empty last segment (trailing slash)
    if (lastSegment === '') {
      return pathSegments[pathSegments.length - 2]
    }

    return lastSegment
  }

  return (
    <header className="bg-background border-b sticky top-0 z-10">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-3">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-lg">TimeTracker</span>
              {currentPath !== '/' && (
                <div className="flex items-center gap-1 text-muted-foreground">
                  <span>/</span>
                  <span className="capitalize">
                    {getPageTitle().replace(/-/g, ' ')}
                  </span>
                </div>
              )}
            </div>
          </div>

          <NavigationMenu>
            <NavigationMenuList className="flex gap-1">
              <NavLink url="/" label="Home" currentPath={currentPath} />
              <NavLink url="/login" label="Login" currentPath={currentPath} />
              <NavLink url="/admin" label="Admin" currentPath={currentPath} />
              <NavLink
                url="/admin/users"
                label="Users"
                currentPath={currentPath}
              />
              <NavLink
                url="/time-records"
                label="Time Records"
                currentPath={currentPath}
              />
              <NavLink url="/timers" label="Timers" currentPath={currentPath} />
              <NavLink
                url="/reports"
                label="Reports"
                currentPath={currentPath}
              />
              <ModeToggle />
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
      <RunningTimerDisplay />
    </header>
  )
}

interface NavLinkProps {
  url: string
  label: string
  currentPath: string
}
const NavLink: FC<NavLinkProps> = ({ url, label, currentPath }) => {
  // Function to check if a link is active
  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === '/'
    }
    return currentPath.startsWith(path)
  }

  return (
    <>
      <NavigationMenuItem>
        <NavigationMenuLink
          asChild
          active={isActive(url)}
          // className={cn(
          //   "px-3 py-2 rounded-md",
          //   isActive(url) ? "bg-primary/10 text-primary" : "hover:bg-muted"
          // )}
        >
          <Link to={url}>{label}</Link>
        </NavigationMenuLink>
      </NavigationMenuItem>
    </>
  )
}
