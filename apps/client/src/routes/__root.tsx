import { Outlet, createRootRoute } from '@tanstack/react-router'
import { IdleTimeMonitor } from '~/components/IdleTimeMonitor'
import { Footer } from '~/components/footer'
import Header from '../components/Header'

export const Route = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  return (
    <div className="min-h-screen flex flex-col overflow-hidden">
      <Header />
      <IdleTimeMonitor />

      <main className="flex-1 px-4 py-1 overflow-y-auto w-full">
        <Outlet />
      </main>
      <Footer />
      {/* <TanStackRouterDevtools /> */}
    </div>
  )
}
