import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '~/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card'
import { useAuth } from '~/modules/auth/use-auth'
import { calculateServerUrl } from '~/modules/urls/url-builder'

export const Route = createFileRoute('/login')({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => ({
    redirect: (search.redirect as string) || '/',
  }),
})

function RouteComponent() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const auth = useAuth()
  const navigate = useNavigate()
  const { redirect } = useSearch({ from: '/login' })

  const handleLogin = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Call the server's login endpoint to get a JWT
      const response = await fetch(`${calculateServerUrl()}/api/login`, {
        method: 'GET',
        credentials: 'include',
      })

      if (response.ok) {
        const result = await response.json()
        if (result.status === 'ok' && result.jwt) {
          // Use the auth context to handle login
          auth.login(result.jwt)

          // Navigate to the redirect URL or home
          await navigate({ to: redirect })
        } else {
          setError('Login failed: Invalid response from server')
        }
      } else {
        setError('Login failed: Server error')
      }
    } catch (err) {
      setError(
        `Login failed: ${err instanceof Error ? err.message : 'Unknown error'}`
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Login</CardTitle>
          <CardDescription>
            Click the button below to authenticate with the application.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
              {error}
            </div>
          )}
          <Button onClick={handleLogin} disabled={isLoading} className="w-full">
            {isLoading ? 'Logging in...' : 'Login'}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
