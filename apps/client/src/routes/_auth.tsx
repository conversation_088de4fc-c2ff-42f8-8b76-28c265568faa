import {
  Outlet,
  createFileRoute,
  useLocation,
  useNavigate,
} from '@tanstack/react-router'
import { type FC, useEffect, useRef } from 'react'

import { useAuth } from '../modules/auth/use-auth'

const AuthLayout: FC = () => {
  const auth = useAuth()
  const navigate = useNavigate()
  const pathname = useLocation({
    select: (location) => location.pathname,
  })
  const pathRef = useRef(pathname)
  pathRef.current = pathname

  const authenticated = auth.isAuthenticated
  useEffect(() => {
    if (authenticated === 'unauthenticated') {
      const redirectUrl = pathRef.current
      console.log('redirecting to login page with path', redirectUrl)
      navigate({
        to: '/login',
        search: {
          redirect: redirectUrl,
        },
      }).catch(console.error)
    }
  }, [authenticated, navigate])

  return (
    <>
      <Outlet />
    </>
  )
}

export const Route = createFileRoute('/_auth')({
  component: AuthLayout,
})
