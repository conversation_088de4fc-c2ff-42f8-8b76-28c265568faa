import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { sumBy } from 'es-toolkit'
import { FileSpreadsheet } from 'lucide-react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import * as v from 'valibot'
import { DataTable } from '~/components/datatable'
import type { TimeRecord } from '~/components/time-record-editor'
import { Button } from '~/components/ui/button'
import { formatDurationFromMs } from '~/lib/utils/date-time'
import { exportTimeRecordsToExcel } from '~/lib/utils/excel-export'
import { createTimeRecordColumns } from '~/modules/timerecords/components/time-records-columns'

// Define the search parameters schema
const searchSchema = v.object({
  startTimestamp: v.number(),
  endTimestamp: v.number(),
  customerId: v.optional(v.string()),
  projectId: v.optional(v.string()),
  taskId: v.optional(v.string()),
  customerName: v.optional(v.string()),
  projectName: v.optional(v.string()),
  taskName: v.optional(v.string()),
})

// Create the route
export const Route = createFileRoute('/_auth/reports/timerecord-details')({
  component: TimerecordDetailsPage,
  validateSearch: searchSchema,
})

function TimerecordDetailsPage() {
  const {
    startTimestamp,
    endTimestamp,
    customerId,
    projectId,
    taskId,
    customerName,
    projectName,
    taskName,
  } = Route.useSearch()
  const navigate = useNavigate()
  const z = useZero<Schema>()

  const [selectedTimeRecordId, setSelectedTimeRecordId] = useState<
    string | undefined
  >(undefined)

  // Current time for running timers
  const [currentTime, setCurrentTime] = useState(Date.now())

  // Update current time every second for running timers
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now())
    }, 1000)
    return () => clearInterval(interval)
  }, [])

  const query = useMemo(() => {
    return z.query.timerecords
      .related('task', (q) =>
        q.related('project', (q) => q.related('customer'))
      )
      .where(({ cmp, and, exists }) => {
        const conditions = [
          cmp('startTimestamp', '>=', startTimestamp),
          cmp('startTimestamp', '<=', endTimestamp),
        ]

        // Add filters if provided
        if (taskId) {
          conditions.push(cmp('taskId', '=', taskId))
        }
        if (projectId) {
          conditions.push(
            exists('task', (iq) =>
              iq.where(({ cmp, and, exists }) =>
                and(
                  cmp('projectId', '=', projectId),
                  customerId
                    ? exists('project', (iq) =>
                        iq.where(({ cmp }) =>
                          cmp('customerId', '=', customerId)
                        )
                      )
                    : undefined
                )
              )
            )
          )
        }

        return and(...conditions)
      })
      .orderBy('startTimestamp', 'asc')
  }, [
    customerId,
    projectId,
    taskId,
    startTimestamp,
    endTimestamp,
    z.query.timerecords,
  ])

  const [timeRecords = [], queryDetails] = useQuery(query, {
    ttl: 'forever',
  })
  const isLoading = queryDetails.type !== 'complete'

  const totalDuration = useMemo(() => {
    return sumBy(timeRecords, (it) => it.endTimestamp - it.startTimestamp)
  }, [timeRecords])

  const totalCost = useMemo(() => {
    return sumBy(timeRecords, (tr) => {
      const duration = tr.endTimestamp - tr.startTimestamp
      const durationInHours = duration / 1000 / 60 / 60
      const rate = tr.task?.project?.customer?.rateValue ?? 0
      return durationInHours * rate
    })
  }, [timeRecords])

  // Create columns for the DataTable
  const columns = useMemo(
    () => createTimeRecordColumns(undefined, currentTime, totalDuration),
    [currentTime, totalDuration]
  )

  // Handle row click (select)
  const handleRowClick = useCallback(
    (row: TimeRecord) => {
      setSelectedTimeRecordId(row.id)

      // Get the date from the time record's startTimestamp
      const date = new Date(row.startTimestamp)
      const dateString = date.toISOString().split('T')[0] // Format as YYYY-MM-DD

      // Navigate to time-records screen with the date and select the time record
      navigate({
        to: '/time-records',
        search: (prev) => ({
          ...prev,
          date: dateString,
          selectedTimeRecordId: row.id,
        }),
      })
    },
    [navigate]
  )

  // Handle row double click (edit)
  const handleRowDoubleClick = useCallback(() => {}, [])

  // Handle back button click
  const handleBackClick = () => {
    navigate({ to: '/reports/timerecord-aggregate' })
  }

  // Build the title based on the filters
  const title = useMemo(() => {
    const parts = []
    if (taskName) parts.push(taskName)
    if (projectName) parts.push(projectName)
    if (customerName) parts.push(customerName)

    if (parts.length === 0) {
      return 'Time Records'
    }

    return parts.join(' - ')
  }, [taskName, projectName, customerName])

  // Format date range for display
  const dateRange = useMemo(() => {
    const start = new Date(startTimestamp).toLocaleDateString()
    const end = new Date(endTimestamp).toLocaleDateString()
    return `${start} to ${end}`
  }, [startTimestamp, endTimestamp])

  // Handle export to Excel
  const handleExportClick = useCallback(async () => {
    if (timeRecords.length > 0) {
      try {
        await exportTimeRecordsToExcel(
          timeRecords as TimeRecord[],
          title,
          dateRange
        )
      } catch (error) {
        console.error('Failed to export Excel file:', error)
      }
    }
  }, [timeRecords, title, dateRange])

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleBackClick}>
            Back
          </Button>
          <h1 className="text-2xl font-bold">{title}</h1>
        </div>
        <div className="flex items-center gap-4">
          {timeRecords.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportClick}
              className="flex items-center gap-2"
            >
              <FileSpreadsheet className="h-4 w-4" />
              Export to Excel
            </Button>
          )}
          <div className="text-sm text-muted-foreground">{dateRange}</div>
        </div>
      </div>

      <div className="mt-4">
        {isLoading ? (
          <div className="text-center p-8 border rounded-md">
            Loading time records...
          </div>
        ) : timeRecords.length > 0 ? (
          <>
            <DataTable
              columns={columns}
              data={timeRecords as TimeRecord[]}
              onRowClick={handleRowClick}
              onRowDoubleClick={handleRowDoubleClick}
              getRowId={(record: TimeRecord) => record.id}
              selectedRowId={selectedTimeRecordId ?? undefined}
              stickyHeaders
            />
            <div className="mt-4 text-right font-medium">
              <ol>
                <li>Total Duration: {formatDurationFromMs(totalDuration)}</li>
                <li>
                  Total Duration [h]:{' '}
                  {(totalDuration / 1000 / 60 / 60).toFixed(2)}
                </li>
                <li>Total Cost: {totalCost.toFixed(2)}</li>
              </ol>
            </div>
          </>
        ) : (
          <div className="text-center p-8 border rounded-md">
            No time records found for the selected filters.
          </div>
        )}
      </div>
    </div>
  )
}
