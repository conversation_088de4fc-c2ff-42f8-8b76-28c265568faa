import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link, createFileRoute } from '@tanstack/react-router'
import { Bar<PERSON>hart } from 'lucide-react'

export const Route = createFileRoute('/_auth/reports/')({
  component: ReportsIndexPage,
})

function ReportsIndexPage() {
  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Reports</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart className="h-5 w-5" />
              Time Aggregation
            </CardTitle>
            <CardDescription>
              Aggregate time records by customer, project, and task
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">
              View total time spent across different customers, projects, and
              tasks within a specified date range.
            </p>
            <Button asChild>
              <Link to="/reports/timerecord-aggregate">View Report</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
