import type { Project, Schema, Task } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useDebounce } from '@uidotdev/usehooks'
import {
  type FC,
  type RefObject,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import * as v from 'valibot'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { TaskEditor } from '~/components/task-editor'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { createGlobalKeyboardHandler } from '~/lib/utils/keyboard'
import { useTaskManagement } from '~/modules/tasks/task-hooks'

// Define search schema for URL parameters
const searchSchema = v.object({
  projectId: v.optional(v.string()),
})

export const Route = createFileRoute('/_auth/admin/tasks/')({
  component: RouteComponent,
  validateSearch: searchSchema,
})

// Utility functions
function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Define columns for the DataTable
function createColumns(projects: Project[]): ColumnDef<Task>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'projectId',
      header: 'Project',
      cell: ({ row }) => {
        const project = projects.find((p) => p.id === row.original.projectId)
        return project?.name || '-'
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
    },
    {
      accessorKey: 'defaultTask',
      header: 'Default',
      cell: ({ row }) => (row.original.defaultTask ? 'Yes' : 'No'),
    },
    {
      accessorKey: 'pinned',
      header: 'Pinned',
      cell: ({ row }) => (row.original.pinned ? 'Yes' : 'No'),
    },
    {
      accessorKey: 'lastUsed',
      header: 'Last Used',
      cell: ({ row }) => formatDate(row.original.lastUsed),
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({ row }) => formatDate(row.original.updatedAt),
    },
  ]
}

interface TaskTableProps {
  tasks: Task[]
  selectedTaskId: string | null
  onRowClick: (taskId: string) => void
  onRowDoubleClick?: (taskId: string) => void
  tableRef?: RefObject<DataTableRef | null>
  onSelectedTaskIdChanged?: (taskId: string | undefined) => void
}

// Task table component
const TaskTable: FC<TaskTableProps> = ({
  tasks,
  selectedTaskId,
  onRowClick,
  onRowDoubleClick,
  onSelectedTaskIdChanged,
  tableRef,
}) => {
  const z = useZero<Schema>()

  // Fetch all projects to display project names
  const [projects = []] = useQuery(z.query.projects, {
    ttl: 'forever',
  })

  const columns = useMemo(() => createColumns(projects), [projects])

  return (
    <div className="rounded-md border">
      <DataTable
        ref={tableRef}
        columns={columns}
        data={tasks}
        onRowClick={(task: Task) => onRowClick(task.id)}
        onRowDoubleClick={
          onRowDoubleClick
            ? (task: Task) => onRowDoubleClick(task.id)
            : undefined
        }
        onFocusedRowIdChanged={onSelectedTaskIdChanged}
        getRowId={(task: Task) => task.id}
        selectedRowId={selectedTaskId ?? undefined}
      />
    </div>
  )
}

// Project filter component
interface ProjectFilterProps {
  projects: Project[]
  selectedProjectId: string | undefined
  onProjectChange: (projectId: string | undefined) => void
}

const ProjectFilter: FC<ProjectFilterProps> = ({
  projects,
  selectedProjectId,
  onProjectChange,
}) => {
  // Use "all" as a special value for showing all projects
  const selectValue = selectedProjectId || 'all'

  return (
    <div className="flex items-center gap-4 mb-4">
      <div className="flex items-center gap-2">
        <label htmlFor="project-filter" className="font-medium text-sm">
          Filter by project:
        </label>
        <Select
          value={selectValue}
          onValueChange={(value) =>
            onProjectChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[200px]" id="project-filter">
            <SelectValue placeholder="All projects" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All projects</SelectItem>
            {projects.map((project) => (
              <SelectItem key={project.id} value={project.id}>
                {project.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {selectedProjectId && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onProjectChange(undefined)}
        >
          Clear Filter
        </Button>
      )}
    </div>
  )
}

function RouteComponent() {
  const z = useZero<Schema>()
  const { projectId } = Route.useSearch()
  const navigate = Route.useNavigate()
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const tableRef = useRef<DataTableRef>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Create a query with search and project filter if needed
  const tasksQuery = useMemo(() => {
    let query = z.query.tasks

    // Apply filters
    if (debouncedSearchQuery.length >= 2 || projectId) {
      query = query.where(({ cmp, and }) => {
        const conditions = []

        // Add name search condition if search query is provided
        if (debouncedSearchQuery.length >= 2) {
          const searchPattern = `%${debouncedSearchQuery}%`
          conditions.push(cmp('name', 'ILIKE', searchPattern))
        }

        // Add project filter condition if projectId is provided
        if (projectId) {
          conditions.push(cmp('projectId', '=', projectId))
        }

        // Combine conditions with AND if both filters are applied, otherwise just return the single condition
        return conditions.length > 1 ? and(...conditions) : conditions[0]
      })
    }

    // Default sorting
    query = query.orderBy('name', 'asc')

    return query
  }, [z, debouncedSearchQuery, projectId])

  // Fetch tasks with the query
  const [tasks = []] = useQuery(tasksQuery, {
    ttl: debouncedSearchQuery.length >= 2 ? 0 : 'forever', // Don't cache search results
  })

  // Fetch all projects for the filter
  const [projects = []] = useQuery(z.query.projects, {
    ttl: 'forever',
  })

  // Use the task management hook
  const { handleSave, handleDelete } = useTaskManagement(z, tasks)

  // Get the selected task
  const selectedTask = tasks.find((task) => task.id === selectedTaskId) || null

  // Handle project filter change
  const handleProjectChange = (newProjectId: string | undefined) => {
    navigate({
      search: (prev) => ({
        ...prev,
        projectId: newProjectId,
      }),
    })
  }

  // Handle row click (navigate to task detail page)
  const handleRowClick = (taskId: string) => {
    setSelectedTaskId(taskId)
    navigate({ to: `/admin/tasks/${taskId}` })
  }

  // Handle row double click (edit)
  const handleRowDoubleClick = (taskId: string) => {
    setSelectedTaskId(taskId)
    setIsEditorOpen(true)
  }

  // Add global keyboard event listener to focus the table when Escape is pressed
  useEffect(() => {
    // Create a handler that only executes if no input element is focused
    const handleGlobalKeyDown = createGlobalKeyboardHandler((e) => {
      // Focus the table when Escape is pressed
      if (e.key === 'Escape' && !isEditorOpen) {
        e.preventDefault()
        tableRef.current?.focus()
      }
    })

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [isEditorOpen])

  // Add new task
  const handleAddNew = () => {
    setSelectedTaskId(null)
    setIsEditorOpen(true)
  }

  // Edit selected task
  const handleEditSelected = () => {
    if (selectedTaskId) {
      setIsEditorOpen(true)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Tasks</h1>
        <div className="flex gap-2">
          {selectedTaskId && (
            <Button onClick={handleEditSelected}>Edit Selected</Button>
          )}
          <Button onClick={handleAddNew}>Add New</Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <ProjectFilter
          projects={projects}
          selectedProjectId={projectId}
          onProjectChange={handleProjectChange}
        />

        <div className="flex-1 flex items-center">
          <Input
            placeholder="Search tasks by name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              onClick={() => setSearchQuery('')}
              className="ml-2"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      <TaskTable
        tasks={tasks}
        selectedTaskId={selectedTaskId}
        onRowClick={handleRowClick}
        onRowDoubleClick={handleRowDoubleClick}
        onSelectedTaskIdChanged={(taskId) => {
          setSelectedTaskId(taskId ?? null)
        }}
        tableRef={tableRef}
      />

      {isEditorOpen && (
        <TaskEditor
          task={selectedTask}
          projectId={projectId}
          isOpen={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSave={handleSave}
          onDelete={handleDelete}
        />
      )}
    </div>
  )
}
