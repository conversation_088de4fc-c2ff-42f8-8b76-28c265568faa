import React from 'react'
import ReactDOM from 'react-dom/client'

import { ThemeProvider } from '@/components/theme-provider'
import { schema } from '@ftt/shared'
import { Zero } from '@rocicorp/zero'
import { ZeroProvider } from '@rocicorp/zero/react'

import './styles.css'

import { getJwtPayload } from './modules/auth/jwt-storage'

const { userID, encodedJWT } = getJwtPayload()
// In a real app, you might initialize this inside of useMemo
// and use a real auth token
const z = new Zero({
  userID,
  auth: () => encodedJWT,
  server: import.meta.env.VITE_PUBLIC_SERVER,
  schema,
  kvStore: 'idb',
})

import { RouterProvider, createRouter } from '@tanstack/react-router'
import { StateInitializer } from './components/state/state-initializer.tsx'
import { Toaster } from './components/ui/sonner.tsx'
import { AuthProvider } from './modules/auth/use-auth'
import reportWebVitals from './reportWebVitals.ts'
// router stuff
import { routeTree } from './routeTree.gen'

// Create a new router instance
const router = createRouter({
  routeTree,
  context: {},
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('root')
if (rootElement && !rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <React.StrictMode>
      <ZeroProvider zero={z}>
        <StateInitializer />
        <AuthProvider>
          <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
            <RouterProvider router={router} />
            <Toaster />
          </ThemeProvider>
        </AuthProvider>
      </ZeroProvider>
    </React.StrictMode>
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals(console.log)
