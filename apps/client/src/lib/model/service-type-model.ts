import type { ComboboxOption } from '~/components/combobox'

export const ServiceType = {
  JIRA_CLOUD: 'Jira Cloud',
  JIRA_SERVER: 'Jira Server',
  GOFORE_TIMESHEET: 'Gofore Timesheet',
} as const
export const ServiceTypeLabels = {
  JIRA_CLOUD: 'Jira Cloud',
  JIRA_SERVER: 'Jira Server',
  GOFORE_TIMESHEET: 'Gofore Timesheet',
} as const
export type ServiceType = (typeof ServiceType)[keyof typeof ServiceType]

export const ServiceTypeOptions: ComboboxOption[] = Object.entries(
  ServiceTypeLabels
).map(([key, value]) => ({
  value: key,
  label: value,
}))
