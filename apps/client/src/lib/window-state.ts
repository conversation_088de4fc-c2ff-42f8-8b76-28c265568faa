import { invoke } from '@tauri-apps/api/core'

/**
 * Check if the app window is currently focused (in foreground)
 * @returns A promise that resolves to true if the window is focused
 */
export async function isWindowFocused(): Promise<boolean> {
  try {
    return await invoke<boolean>('is_window_focused')
  } catch (error) {
    console.error('Failed to check window focus state:', error)
    return false
  }
}