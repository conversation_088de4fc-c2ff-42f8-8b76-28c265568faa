import { invoke } from '@tauri-apps/api/core'

export function supportsInvoke() {
  if (typeof window !== 'undefined') {
    return '__TAURI_INTERNALS__' in window && !!window.__TAURI_INTERNALS__
  }
  return false
}

/**
 * Get the system idle time in milliseconds
 * @returns A promise that resolves to the idle time in milliseconds
 */
export async function getIdleTime(): Promise<number> {
  try {
    return await invoke<number>('get_idle_time')
  } catch (error) {
    console.error('Failed to get idle time:', error)
    throw error
  }
}

/**
 * Check if the system has been idle for at least the specified duration
 * @param durationMs The minimum idle duration in milliseconds
 * @returns A promise that resolves to true if the system has been idle for at least the specified duration
 */
export async function hasBeenIdleFor(durationMs: number): Promise<boolean> {
  const idleTimeMs = await getIdleTime()
  return idleTimeMs >= durationMs
}
