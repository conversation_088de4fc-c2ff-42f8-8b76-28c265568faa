import { type RefObject, useCallback, useEffect } from 'react'
import type { TimeRecordsTableRef } from '~/modules/timerecords/components/time-records-table'
import { getNextDay, getPreviousDay } from '../utils/date-time'
import { createGlobalKeyboardHandler } from '../utils/keyboard'
import { useRefify } from './use-refify'

interface KeyboardNavigationOptions {
  navigateRef: RefObject<
    (options: {
      search: (prev: { date?: string }) => { date?: string }
    }) => void
  >
  selectedDateRef: RefObject<string>
  timeRecordsRef: RefObject<unknown[]>
  selectedRowIndexRef: RefObject<number>
  setSelectedTimeRecordId: (id: string | undefined) => void
  handleStopTimer: () => void
  handleRowAction: (index: number) => void
  setIsTimerDialogOpen: (isOpen: boolean) => void
  tableRef?: RefObject<TimeRecordsTableRef | null>
  openRunningTimerEditor?: () => void
}

/**
 * Custom hook for handling keyboard navigation in the time records view
 */
export function useKeyboardNavigation({
  navigateRef,
  selectedDateRef,
  timeRecordsRef,
  selectedRowIndexRef,
  setSelectedTimeRecordId,
  handleStopTimer,
  handleRowAction,
  setIsTimerDialogOpen,
  tableRef,
  openRunningTimerEditor,
}: KeyboardNavigationOptions) {
  /**
   * Handle keyboard events for the table
   */
  const handleTableKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      // Handle vertical navigation (rows)
      if (e.key === 'ArrowUp') {
        e.preventDefault()
        // Move selection up
        if (timeRecordsRef.current && timeRecordsRef.current.length > 0) {
          const currentIndex = selectedRowIndexRef.current
          const newIndex = currentIndex <= 0 ? 0 : currentIndex - 1
          const records = timeRecordsRef.current as { id: string }[]
          if (newIndex >= 0 && newIndex < records.length) {
            setSelectedTimeRecordId(records[newIndex].id)
          }
        }
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        // Move selection down
        if (timeRecordsRef.current && timeRecordsRef.current.length > 0) {
          const currentIndex = selectedRowIndexRef.current
          const records = timeRecordsRef.current as { id: string }[]
          let newIndex = currentIndex === -1 ? 0 : currentIndex + 1
          if (newIndex >= records.length) {
            newIndex = records.length - 1
          }
          if (newIndex >= 0 && newIndex < records.length) {
            setSelectedTimeRecordId(records[newIndex].id)
          }
        }
      } else if (e.key === 'Enter') {
        e.preventDefault()
        // Perform action on selected row
        if (
          selectedRowIndexRef.current !== null &&
          selectedRowIndexRef.current >= 0
        ) {
          handleRowAction(selectedRowIndexRef.current)
        }
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault()
        // Navigate to previous day
        navigateRef.current?.({
          search: (prev: { date?: string }) => ({
            ...prev,
            date: getPreviousDay(selectedDateRef.current || ''),
          }),
        })
        // Reset row selection when changing dates
        setSelectedTimeRecordId(undefined)
      } else if (e.key === 'ArrowRight') {
        e.preventDefault()
        // Navigate to next day
        navigateRef.current?.({
          search: (prev: { date?: string }) => ({
            ...prev,
            date: getNextDay(selectedDateRef.current || ''),
          }),
        })
        // Reset row selection when changing dates
        setSelectedTimeRecordId(undefined)
      }
    },
    [
      navigateRef,
      selectedDateRef,
      timeRecordsRef,
      selectedRowIndexRef,
      setSelectedTimeRecordId,
      handleRowAction,
    ]
  )

  const handleStopTimerRef = useRefify(handleStopTimer)
  const openRunningTimerEditorRef = useRefify(openRunningTimerEditor)
  /**
   * Handle global keyboard shortcuts
   */
  const handleGlobalKeyDown = useCallback(() => {
    // Create a handler that only executes if no input element is focused
    return createGlobalKeyboardHandler((e: KeyboardEvent) => {
      // Only handle global shortcuts here
      if (e.metaKey || e.ctrlKey) {
        if (e.key === '.' || e.key === 'l') {
          e.preventDefault()
          setIsTimerDialogOpen(true)
        } else if (e.key === 'o') {
          e.preventDefault()
          handleStopTimerRef.current()
        } else if (e.key === 'e' && openRunningTimerEditorRef.current) {
          e.preventDefault()
          openRunningTimerEditorRef.current()
        }
      } else if (e.key === 'Escape') {
        // Focus the table when Escape is pressed
        tableRef?.current?.focus()
      }
    })
  }, [
    setIsTimerDialogOpen,
    tableRef,
    handleStopTimerRef,
    openRunningTimerEditorRef,
  ])

  // Set up the global event listeners once
  useEffect(() => {
    const keydownHandler = handleGlobalKeyDown()
    window.addEventListener('keydown', keydownHandler)
    return () => {
      window.removeEventListener('keydown', keydownHandler)
    }
  }, [handleGlobalKeyDown])

  // Focus the table when the component mounts
  useEffect(() => {
    // Small delay to ensure the table is rendered
    const timeoutId = setTimeout(() => {
      tableRef?.current?.focus()
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [tableRef])

  return { handleTableKeyDown }
}
