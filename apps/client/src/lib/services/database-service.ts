import type { Schema } from '@ftt/shared'
import type { DBMutator, Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import { uuidv7 } from 'uuidv7'
import type { TimeRecordInput } from '~/modules/timerecords/time-record-model'
import type { TimerInput } from '~/modules/timers/timer-model'
import type { TimeTrackerState, TimerStruct } from '../model/app-state'
import { RemoteServiceManager } from './remote-service-manager'

export async function dbStopTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  timerId: string,
  stopTimestamp: number = Date.now()
): Promise<string> {
  console.log('dbservice stopping timer', z.userID, { timerId, stopTimestamp })
  const timer = await z.query.timers.where('id', '=', timerId).one().run()
  invariant(timer, 'Timer not found')
  const timeRecordId = timer.worklogId
  console.log('got timer', timer)
  const stopTime =
    timer.status === 'running' ? stopTimestamp : timer.endTimestamp
  const now = Date.now()
  try {
    console.log('dbservice updating timerecord', timeRecordId)
    await tx.timerecords
      .update({
        id: timeRecordId,
        endTimestamp: stopTime,
        updatedAt: now,
        updatedBy: z.userID,
      })
      .catch((error) => {
        console.error('Error stopping timerecords:', error)
      })
    console.log('dbservice stopping timer', timerId)
    await tx.timers.update({
      id: timerId,
      status: 'stopped',
      endTimestamp: stopTimestamp,
      updatedAt: now,
      updatedBy: z.userID,
    })
    return timeRecordId
  } catch (error) {
    console.error('Error stopping timer and timerecord:', error)
    throw error
  }
}

export async function dbDeleteTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  timerId: string
): Promise<void> {
  console.log('dbservice deleting timer', timerId, z.userID)

  try {
    await tx.timers.delete({ id: timerId })
  } catch (error) {
    console.error('Error deleting:', error)
    throw error
  }
}

export async function dbResumeTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  timerId: string,
  timeRecordId: string
) {
  console.log('dbResumeTimer', timerId, timeRecordId)
  const now = Date.now()
  await tx.timers.update({
    id: timerId,
    status: 'running',
    endTimestamp: null,
    updatedAt: now,
    updatedBy: z.userID,
  })
  console.log('updating time record id:', timeRecordId)
  await tx.timerecords.update({
    id: timeRecordId,
    endTimestamp: null,
    updatedAt: now,
    updatedBy: z.userID,
  })
}

export async function dbUpdateTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  timerId: string,
  timerInput: TimerInput
): Promise<void> {
  console.log('dbservice updating timer', timerId, timerInput)
  const now = Date.now()
  try {
    await tx.timers.update({
      id: timerId,
      status: timerInput.status,
      updatedAt: now,
      updatedBy: z.userID,
      startTimestamp: timerInput.startTimestamp,
      endTimestamp: timerInput.endTimestamp,
    })

    console.log(
      'updating time record id:',
      timerInput.timeRecordId,
      'end timestamp:',
      timerInput.endTimestamp
    )
    await tx.timerecords.update({
      id: timerInput.timeRecordId,
      startTimestamp: timerInput.startTimestamp,
      endTimestamp: timerInput.endTimestamp,
      updatedAt: now,
      updatedBy: z.userID,
    })
  } catch (error) {
    console.error('Error updating timer and timerecord:', error)
    throw error
  }
}

/**
 * Create a new timer and timerecord
 */
export async function dbCreateTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  taskId: string
): Promise<TimerStruct> {
  invariant(taskId, 'No task selected')

  console.log('dbservice creating timer', taskId, z.userID)

  const currentTime = Date.now()
  const timerecordId = uuidv7()
  const timerId = uuidv7()
  const task = await z.query.tasks.where('id', '=', taskId).one().run()
  invariant(task, 'Task not found')

  try {
    // First create the timerecord
    await tx.timerecords.insert({
      id: timerecordId,
      taskId: taskId,
      startTimestamp: currentTime,
      endTimestamp: null,
      createdAt: currentTime,
      updatedAt: currentTime,
      createdBy: z.userID,
      updatedBy: z.userID,
    })

    // Then create the timer - in running state
    await tx.timers.insert({
      id: timerId,
      worklogId: timerecordId,
      status: 'running',
      userId: z.userID,
      startTimestamp: currentTime,
      endTimestamp: null,
      createdAt: currentTime,
      updatedAt: currentTime,
      createdBy: z.userID,
      updatedBy: z.userID,
    })

    // Update the task's lastUsed timestamp
    await tx.tasks.update({
      id: taskId,
      lastUsed: currentTime,
      updatedAt: currentTime,
      updatedBy: z.userID,
    })

    return {
      id: timerId,
      worklogId: timerecordId,
      status: 'running',
    }
  } catch (error) {
    console.error('Error creating timer and timerecord:', error)
    throw error
  }
}

/**
 * Save a time record (create or update)
 */
export async function dbUpdateTimeRecord(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  timeRecord: TimeRecordInput
): Promise<void> {
  console.log('dbservice update time record', timeRecord)
  // Update existing time record
  await tx.timerecords.update({
    id: timeRecord.id,
    taskId: timeRecord.taskId,
    startTimestamp: timeRecord.startTimestamp,
    endTimestamp: timeRecord.endTimestamp,
    comment: timeRecord.comment,
    updatedAt: Date.now(),
    updatedBy: z.userID,
  })
}

export async function dbCreateTimeRecord(
  z: Zero<Schema>,
  timeRecord: TimeRecordInput
): Promise<void> {
  // Create new time record
  await z.mutate.timerecords.insert({
    ...timeRecord,
    id: timeRecord.id || uuidv7(),
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: z.userID,
    updatedBy: z.userID,
  })
}

export async function dbCreateAppState(
  z: Zero<Schema>,
  appState: TimeTrackerState
): Promise<void> {
  console.log('dbCreateAppState', appState)
  await z.mutate.appState.insert({
    id: z.userID,
    isEditDialogOpen: appState.isEditDialogOpen,
    timestampOfIdleTimeStart: appState.timestampOfIdleTimeStart,
    showingTimeoutMessage: appState.showingTimeoutMessage,
    runningTimerId: appState.runningTimer?.id,
  })
}

/**
 * Delete a time record and sync with remote service
 */
export async function dbDeleteTimeRecord(
  z: Zero<Schema>,
  timeRecordId: string
): Promise<void> {
  try {
    // First get the full time record with upload data
    const timeRecord = await z.query.timerecords
      .where('id', '=', timeRecordId)
      .one()
      .run()

    if (!timeRecord) {
      console.warn(`Time record with ID ${timeRecordId} not found`)
      return
    }

    // FIXME the upload handling needs to move into app-state
    // If the time record has upload data, delete it from the remote service
    if (timeRecord.uploadData) {
      const remoteServiceId = timeRecord.uploadData.remoteServiceId
      if (remoteServiceId) {
        // Get the remote service
        const remoteService = await z.query.remoteServices
          .where('id', '=', remoteServiceId)
          .one()
          .run()

        if (remoteService) {
          // Delete from remote service
          const remoteServiceManager = new RemoteServiceManager(z)
          await remoteServiceManager.deleteWorklog(timeRecord, remoteService)
        }
      }
    }

    // Delete the time record locally
    await z.mutate.timerecords.delete({
      id: timeRecordId,
    })
  } catch (error) {
    console.error('Error deleting time record:', error)
    throw error
  }
}
