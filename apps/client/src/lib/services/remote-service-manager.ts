import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { ServiceType } from '~/lib/model/service-type-model'
import { JiraServerAdapter } from './remote-adapters/jira-server-adapter'
import type {
  <PERSON><PERSON><PERSON><PERSON>ult,
  RemoteAdapter,
  RemoteServiceObject,
  RemoteServiceProvider,
  WorklogObject,
} from './remote-adapters/remote-adapter-types'

/**
 * Manager for remote service adapters
 * Handles adapter selection and operations
 */
export class RemoteServiceManager implements RemoteServiceProvider {
  private adapters: Map<ServiceType, RemoteAdapter> = new Map()
  private z: Zero<Schema>

  constructor(z: Zero<Schema>) {
    this.z = z
    this.registerAdapters()
  }

  /**
   * Registers all available adapters
   */
  private registerAdapters(): void {
    // Register Jira Cloud adapter
    // this.adapters.set(ServiceType.JIRA_CLOUD, new JiraCloudAdapter())

    // Register Jira Server adapter
    this.adapters.set(ServiceType.JIRA_SERVER, new JiraServerAdapter())

    // Additional adapters can be registered here
  }

  /**
   * Gets an adapter for the specified service type
   */
  getAdapter(serviceType: ServiceType): RemoteAdapter | null {
    return this.adapters.get(serviceType) || null
  }

  /**
   * Gets an adapter for the specified remote service
   */
  getAdapterForService(
    remoteService: RemoteServiceObject
  ): RemoteAdapter | null {
    return this.getAdapter(remoteService.serviceType as ServiceType)
  }

  /**
   * Lists all remote services for a service type
   */
  async listRemoteServicesForServiceType(
    serviceType: ServiceType
  ): Promise<RemoteServiceObject[]> {
    const remoteServices = await this.z.query.remoteServices
      .where('serviceType', '=', serviceType)
      .run()
    return remoteServices
  }

  /**
   * Adds a worklog to a remote service
   */
  async addWorklog(
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date } | null> {
    const adapter = this.getAdapterForService(remoteService)
    if (!adapter) {
      console.error(
        `No adapter found for service type: ${remoteService.serviceType}`
      )
      return null
    }

    try {
      return await adapter.addWorklog(this.z, worklog, remoteService)
    } catch (error) {
      console.error('Error adding worklog to remote service:', error)
      return null
    }
  }

  /**
   * Updates a worklog on a remote service
   */
  async updateWorklog(
    _worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date } | null> {
    const adapter = this.getAdapterForService(remoteService)
    if (!adapter) {
      console.error(
        `No adapter found for service type: ${remoteService.serviceType}`
      )
      return null
    }

    try {
      // Get the current remote worklog if available
      // const remoteWorklog: RemoteWorklog | null = null
      // const uploadData = worklog.uploadData || {}

      // FIXME properly implement
      // biome-ignore lint/correctness/noConstantCondition: <explanation>
      if (true) {
        throw new Error('not yet implemented')
      }
      // const remoteWorklogId = uploadData.remoteWorklogId
      // const taskCatalogId = uploadData.taskCatalogId

      // if (remoteWorklogId && taskCatalogId) {
      //   const taskCatalog = await this.z.query.taskCatalogs
      //     .where('id', '=', taskCatalogId)
      //     .one()
      //     .run()

      //   if (taskCatalog && adapter) {
      //     try {
      //       invariant(adapter, 'dont have adapter')
      //       invariant(taskCatalog, 'dont have task catalog')
      //       remoteWorklog = await adapter.getWorklog(
      //         this.z,
      //         taskCatalog,
      //         remoteWorklogId,
      //         remoteService
      //       )
      //     } catch (error) {
      //       console.warn('Error getting remote worklog:', error)
      //       // Continue with update even if we can't get the remote worklog
      //     }
      //   }
      // }

      // return await adapter.updateWorklog(
      //   this.z,
      //   worklog,
      //   remoteWorklog,
      //   remoteService
      // )
    } catch (error) {
      console.error('Error updating worklog on remote service:', error)
      return null
    }
  }

  /**
   * Deletes a worklog from a remote service
   */
  async deleteWorklog(
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<boolean> {
    const adapter = this.getAdapterForService(remoteService)
    if (!adapter) {
      console.error(
        `No adapter found for service type: ${remoteService.serviceType}`
      )
      return false
    }

    try {
      const statusCode = await adapter.deleteWorklog(
        this.z,
        worklog,
        remoteService
      )
      return statusCode >= 200 && statusCode < 300
    } catch (error) {
      console.error('Error deleting worklog from remote service:', error)
      return false
    }
  }

  /**
   * Syncs a worklog with a remote service
   * This will add, update, or delete the worklog as needed
   */
  async syncWorklog(worklog: WorklogObject): Promise<boolean> {
    try {
      // Get the linked task catalogs for this worklog
      const timerecordToTaskCatalogs =
        await this.z.query.timerecordToTaskCatalogs
          .where('timerecordId', '=', worklog.id)
          .related('taskCatalog', (q) =>
            q.related('projectCatalog', (q) => q.related('remoteService'))
          )
          .run()

      // If there are no linked task catalogs, nothing to sync
      if (timerecordToTaskCatalogs.length === 0) {
        return true
      }

      // Process each linked task catalog
      for (const link of timerecordToTaskCatalogs) {
        const taskCatalog = link.taskCatalog
        if (
          !taskCatalog ||
          !taskCatalog.projectCatalog ||
          !taskCatalog.projectCatalog.remoteService
        ) {
          continue
        }

        const remoteService = taskCatalog.projectCatalog.remoteService

        // Check if the worklog has already been synced with this remote service
        const uploadData = worklog.uploadData || {}
        const remoteWorklogId = uploadData.remoteWorklogId
        const remoteServiceId = uploadData.remoteServiceId

        if (remoteWorklogId && remoteServiceId === remoteService.id) {
          // Update existing worklog
          await this.updateWorklog(worklog, remoteService)
        } else {
          // Add new worklog
          await this.addWorklog(worklog, remoteService)
        }
      }

      return true
    } catch (error) {
      console.error('Error syncing worklog with remote service:', error)
      return false
    }
  }

  /**
   * Finds an adapter that supports the given URL
   */
  async findAdapterForUrl(url: URL): Promise<RemoteAdapter | undefined> {
    for (const adapter of this.adapters.values()) {
      const supported = await adapter.supportsUrl(url, this)
      if (supported) {
        return adapter
      }
    }
    return undefined
  }

  /**
   * Parses an issue key from a URL
   */
  async parseIssueKeyFromUrl(url: URL): Promise<ParsingResult | undefined> {
    for (const adapter of this.adapters.values()) {
      const result = await adapter.parseIssueKeyFromUrl(url, this)
      if (result) {
        return result
      }
    }
    return undefined
  }
}
