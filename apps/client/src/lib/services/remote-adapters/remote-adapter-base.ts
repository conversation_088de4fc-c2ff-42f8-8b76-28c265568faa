import type { Schema, TaskCatalog } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import type { ServiceType } from '~/lib/model/service-type-model'
import type {
  ParsingResult,
  ProjectCatalogObject,
  RemoteAdapter,
  RemoteServiceObject,
  RemoteServiceProvider,
  RemoteWorklog,
  TaskCatalogObject,
  UploadData,
  WorklogObject,
} from './remote-adapter-types'

/**
 * Base implementation of the RemoteAdapter interface
 * Provides common functionality and default implementations
 */
export abstract class RemoteAdapterBase implements RemoteAdapter {
  abstract serviceType: ServiceType
  abstract needsImportForFoundProject: boolean

  /**
   * Adds a new worklog to the remote service
   */
  abstract addWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }>

  /**
   * Gets a worklog from the remote service
   */
  abstract getWorklog(
    z: Zero<Schema>,
    taskCatalog: TaskCatalogObject,
    worklogId: string,
    remoteService: RemoteServiceObject
  ): Promise<RemoteWorklog>

  /**
   * Updates a worklog on the remote service
   */
  abstract updateWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteWorklog: RemoteWorklog | null,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }>

  /**
   * Updates a worklog and moves it to a different task
   * Default implementation deletes the old worklog and creates a new one
   */
  async updateAndMoveWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    _oldRemoteIssue: TaskCatalogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    // Default implementation: delete old worklog and create new one
    // FIXME we need to pass in the old remote issue key as for jira you need to delete the worklog based on the issue
    await this.deleteWorklog(z, worklog, remoteService)
    return this.addWorklog(z, worklog, remoteService)
  }

  /**
   * Deletes a worklog from the remote service
   */
  abstract deleteWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<number>

  /**
   * Finds all projects on the remote service
   */
  abstract findAllProjects(
    z: Zero<Schema>,
    remoteService: RemoteServiceObject
  ): Promise<ProjectCatalogObject[]>

  /**
   * Finds tasks for a project by key prefix
   */
  abstract findTasksForProjectByKeyStartingWith(
    z: Zero<Schema>,
    keyPrefix: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds tasks for a project by search text
   */
  abstract findTasksForProjectBySearchText(
    z: Zero<Schema>,
    searchText: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds the most recently used tasks for a project
   */
  abstract findMostRecentTasksForProject(
    z: Zero<Schema>,
    projectCatalog: ProjectCatalogObject,
    remoteService: RemoteServiceObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds a task by its key
   */
  abstract findTaskByKey(
    z: Zero<Schema>,
    issueKey: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject | null>

  /**
   * Checks if this adapter supports the given URL
   */
  abstract supportsUrl(
    url: URL,
    provider: RemoteServiceProvider
  ): Promise<boolean>

  /**
   * Parses an issue key from a URL
   */
  abstract parseIssueKeyFromUrl(
    url: URL,
    provider: RemoteServiceProvider
  ): Promise<ParsingResult | undefined>

  /**
   * Tries to authenticate with the remote service
   */
  abstract tryAuthenticate(
    z: Zero<Schema>,
    remoteService: RemoteServiceObject
  ): Promise<boolean>

  /**
   * Helper method to get the linked task catalogs for a worklog
   */
  protected async getLinkedTaskCatalogs(
    z: Zero<Schema>,
    worklogId: string
  ): Promise<TaskCatalog[]> {
    const timerecordToTaskCatalogs = await z.query.timerecordToTaskCatalogs
      .where('timerecordId', '=', worklogId)
      .related('taskCatalog')
      .run()

    return timerecordToTaskCatalogs
      .map((link) => link.taskCatalog)
      .filter((tc): tc is TaskCatalog => tc !== null)
  }

  /**
   * Helper method to get the primary task catalog for a worklog
   * This is typically the first linked task catalog
   */
  protected async getPrimaryTaskCatalog(
    z: Zero<Schema>,
    worklogId: string
  ): Promise<TaskCatalogObject | null> {
    const taskCatalogs = await this.getLinkedTaskCatalogs(z, worklogId)
    return taskCatalogs.length > 0 ? taskCatalogs[0] : null
  }

  /**
   * Helper method to link a worklog to a task catalog
   */
  protected async linkWorklogToTaskCatalog(
    z: Zero<Schema>,
    worklogId: string,
    taskCatalogId: string
  ): Promise<void> {
    const now = Date.now()
    await z.mutate.timerecordToTaskCatalogs.insert({
      timerecordId: worklogId,
      taskCatalogId,
      createdAt: now,
      updatedAt: now,
      createdBy: z.userID,
      updatedBy: z.userID,
    })
  }

  /**
   * Helper method to update a worklog's upload status
   */
  protected async updateWorklogUploadStatus(
    z: Zero<Schema>,
    worklogId: string,
    uploadData: UploadData | null,
    uploadSuccess: boolean
  ): Promise<void> {
    const now = Date.now()
    await z.mutate.timerecords.update({
      id: worklogId,
      uploadData,
      uploadSuccess,
      uploaded: now,
      updatedAt: now,
      updatedBy: z.userID,
    })
  }
}
