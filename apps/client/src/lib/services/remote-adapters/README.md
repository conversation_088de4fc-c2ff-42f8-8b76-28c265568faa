# Remote Service Adapters

This directory contains the implementation of remote service adapters for syncing time records with external services like Jira.

## Architecture

The remote service adapter system is designed to be extensible and pluggable, allowing for easy addition of new remote service types.

### Key Components

1. **RemoteAdapter Interface**: Defines the contract that all remote service adapters must implement.
2. **RemoteAdapterBase**: Abstract base class that provides common functionality for all adapters.
3. **Specific Adapters**: Concrete implementations for specific remote services (e.g., JiraCloudAdapter, JiraServerAdapter).
4. **RemoteServiceManager**: Manages the selection and operation of adapters based on the remote service type.

## Integration Points

The remote service adapters are integrated with the time record management system at the following points:

1. **Time Record Creation**: When a new time record is created, it is synced with the appropriate remote service.
2. **Time Record Updates**: When a time record is updated, the changes are synced with the remote service.
3. **Time Record Deletion**: When a time record is deleted, it is also deleted from the remote service.

## Adding a New Adapter

To add support for a new remote service:

1. Create a new adapter class that extends `RemoteAdapterBase` and implements the `RemoteAdapter` interface.
2. Add the new service type to the `ServiceType` enum in `lib/model/service-type-model.ts`.
3. Register the new adapter in the `RemoteServiceManager.registerAdapters()` method.

## Error Handling

The remote service adapters include robust error handling to ensure that local operations succeed even if remote operations fail. Errors during remote operations are logged but do not prevent local operations from completing.

## Data Flow

1. User creates/updates/deletes a time record in the UI.
2. The time record is saved to the local database.
3. The `RemoteServiceManager` is used to sync the time record with the appropriate remote service.
4. The remote service adapter handles the API calls to the remote service.
5. The result of the remote operation is stored in the time record's `uploadData` field.

## Mock Implementation

The current implementation includes mock adapters for Jira Cloud and Jira Server that simulate the behavior of real adapters without making actual API calls. In a production environment, these would be replaced with real implementations that make API calls to the respective services.

## Future Improvements

1. Add support for more remote services (e.g., Azure DevOps, GitHub Issues).
2. Implement retry logic for failed remote operations.
3. Add a background sync service to retry failed operations periodically.
4. Add a UI to view and manage failed sync operations.
