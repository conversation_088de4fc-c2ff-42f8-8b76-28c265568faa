import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import type { ServiceType } from '~/lib/model/service-type-model'

export type UploadDataRow = {
  taskCatalogId: string
  remoteWorklogId: string
  remoteTimestamp: number
  uploadTimestamp: number
}

export type UploadData = {
  [taskCatalogId: string]: UploadDataRow
}

/**
 * Error types for remote service operations
 */
export enum ProtocolErrorType {
  NO_RESPONSE_RECEIVED = 'noResponseReceived',
  WORKLOG_HAS_BEEN_REMOTELY_UPDATED = 'worklogHasBeenRemotelyUpdated',
  WORKLOG_NOT_FOUND = 'worklogNotFound',
}

export class ProtocolError extends Error {
  type: ProtocolErrorType
  remoteWorklog?: RemoteWorklog

  constructor(
    type: ProtocolErrorType,
    message: string,
    remoteWorklog?: RemoteWorklog
  ) {
    super(message)
    this.type = type
    this.remoteWorklog = remoteWorklog
    this.name = 'ProtocolError'
  }
}

/**
 * Represents a worklog on a remote service
 */
export interface RemoteWorklog {
  startTime: Date
  endTime: Date
  comment: string
  updateAuthor: string
  remoteWorklogId: string
  created: Date
  modified: Date
}

/**
 * Represents a remote service configuration
 */
export interface RemoteServiceObject {
  id: string
  name: string
  serviceType: ServiceType
  remoteUrl: string
  remoteUser: string
  remotePassword: string
  createdAt?: number
  updatedAt?: number
  createdBy?: string
  updatedBy?: string
}

/**
 * Represents a project catalog from a remote service
 */
export interface ProjectCatalogObject {
  id: string
  name: string
  key?: string
  remoteId?: string
  remoteUrl?: string
  remoteServiceId?: string
  createdAt?: number
  updatedAt?: number
  createdBy?: string
  updatedBy?: string
}

/**
 * Represents a task catalog from a remote service
 */
export interface TaskCatalogObject {
  id: string
  name: string
  key?: string
  status?: string
  remoteId?: string
  remoteUrl?: string
  projectCatalogId?: string
  lastUsed?: number
  pinned?: boolean
  createdAt?: number
  updatedAt?: number
  createdBy?: string
  updatedBy?: string
}

/**
 * Represents a worklog to be synced with a remote service
 */
export interface WorklogObject {
  id: string
  taskId: string
  startTimestamp: number
  endTimestamp: number | null
  comment?: string
  uploadData?: UploadData
  uploadSuccess?: boolean
  uploaded?: number
  createdAt?: number
  updatedAt?: number
  createdBy?: string
  updatedBy?: string
  task?: {
    id: string
    name: string
    project?: {
      id: string
      name: string
      customerId?: string
    }
  }
}

/**
 * Result of parsing an issue key from a URL
 */
export interface ParsingResult {
  issueKey: string
  projectKey: string
  remoteServiceObject: RemoteServiceObject
}

/**
 * Provider interface for listing remote services
 */
export interface RemoteServiceProvider {
  listRemoteServicesForServiceType(
    serviceType: ServiceType
  ): Promise<RemoteServiceObject[]>
}

/**
 * Generic interface for remote service adapters
 */
export interface RemoteAdapter {
  /**
   * The type of service this adapter supports
   */
  serviceType: ServiceType

  /**
   * Whether this adapter needs to import projects when found
   */
  needsImportForFoundProject: boolean

  /**
   * Adds a new worklog to the remote service
   * @param z Zero instance for database access
   * @param worklog The worklog to add
   * @param remoteService The remote service configuration
   * @returns Promise with the remote worklog ID and timestamp
   */
  addWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }>

  /**
   * Gets a worklog from the remote service
   * @param z Zero instance for database access
   * @param taskCatalog The task catalog to get the worklog from
   * @param worklogId The ID of the worklog on the remote service
   * @param remoteService The remote service configuration
   * @returns Promise with the remote worklog
   */
  getWorklog(
    z: Zero<Schema>,
    taskCatalog: TaskCatalogObject,
    worklogId: string,
    remoteService: RemoteServiceObject
  ): Promise<RemoteWorklog>

  /**
   * Updates a worklog on the remote service
   * @param z Zero instance for database access
   * @param worklog The worklog to update
   * @param remoteWorklog The current remote worklog state (if available)
   * @param remoteService The remote service configuration
   * @returns Promise with the remote worklog ID and timestamp
   */
  updateWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteWorklog: RemoteWorklog | null,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }>

  /**
   * Updates a worklog and moves it to a different task
   * @param z Zero instance for database access
   * @param worklog The worklog to update
   * @param oldRemoteIssue The previous task catalog
   * @param remoteService The remote service configuration
   * @returns Promise with the remote worklog ID and timestamp
   */
  updateAndMoveWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    oldRemoteIssue: TaskCatalogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }>

  /**
   * Deletes a worklog from the remote service
   * @param z Zero instance for database access
   * @param worklog The worklog to delete
   * @param remoteService The remote service configuration
   * @returns Promise with the status code
   */
  deleteWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<number>

  /**
   * Finds all projects on the remote service
   * @param z Zero instance for database access
   * @param remoteService The remote service configuration
   * @returns Promise with the list of project catalogs
   */
  findAllProjects(
    z: Zero<Schema>,
    remoteService: RemoteServiceObject
  ): Promise<ProjectCatalogObject[]>

  /**
   * Finds tasks for a project by key prefix
   * @param z Zero instance for database access
   * @param keyPrefix The key prefix to search for
   * @param projectCatalog The project catalog to search in
   * @returns Promise with the list of task catalogs
   */
  findTasksForProjectByKeyStartingWith(
    z: Zero<Schema>,
    keyPrefix: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds tasks for a project by search text
   * @param z Zero instance for database access
   * @param searchText The text to search for
   * @param projectCatalog The project catalog to search in
   * @returns Promise with the list of task catalogs
   */
  findTasksForProjectBySearchText(
    z: Zero<Schema>,
    searchText: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds the most recently used tasks for a project
   * @param z Zero instance for database access
   * @param projectCatalog The project catalog to search in
   * @param remoteService The remote service configuration
   * @returns Promise with the list of task catalogs
   */
  findMostRecentTasksForProject(
    z: Zero<Schema>,
    projectCatalog: ProjectCatalogObject,
    remoteService: RemoteServiceObject
  ): Promise<TaskCatalogObject[]>

  /**
   * Finds a task by its key
   * @param z Zero instance for database access
   * @param issueKey The key of the task to find
   * @param projectCatalog The project catalog to search in
   * @returns Promise with the task catalog
   */
  findTaskByKey(
    z: Zero<Schema>,
    issueKey: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject | null>

  /**
   * Checks if this adapter supports the given URL
   * @param url The URL to check
   * @param provider The remote service provider
   * @returns Whether this adapter supports the URL
   */
  supportsUrl(url: URL, provider: RemoteServiceProvider): Promise<boolean>

  /**
   * Parses an issue key from a URL
   * @param url The URL to parse
   * @param provider The remote service provider
   * @returns The parsing result or null if no issue key was found
   */
  parseIssueKeyFromUrl(
    url: URL,
    provider: RemoteServiceProvider
  ): Promise<ParsingResult | undefined>

  /**
   * Tries to authenticate with the remote service
   * @param z Zero instance for database access
   * @param remoteService The remote service configuration
   * @returns Promise with the authentication result
   */
  tryAuthenticate(
    z: Zero<Schema>,
    remoteService: RemoteServiceObject
  ): Promise<boolean>
}
