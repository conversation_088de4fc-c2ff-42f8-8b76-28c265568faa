/**
 * Conditionally join class names based on an object where keys are class names and values are booleans
 * @param classObj An object where keys are class names and values are booleans indicating whether to include the class
 * @returns A string of all enabled class names separated by space
 */
export function cnByObject(classObj: Record<string, boolean>): string {
  return Object.entries(classObj)
    .filter(([_, enabled]) => enabled)
    .map(([className]) => className)
    .join(' ')
}
