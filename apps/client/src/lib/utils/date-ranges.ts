/**
 * Utility functions for calculating common date ranges
 */

/**
 * Get the date range for today
 * @returns Object with start and end dates for today
 */
export function getTodayRange(): { startDate: Date; endDate: Date } {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return {
    startDate: today,
    endDate: today,
  }
}

/**
 * Get the date range for the current week (Monday to Sunday)
 * @returns Object with start and end dates for the current week
 */
export function getCurrentWeekRange(): { startDate: Date; endDate: Date } {
  const today = new Date()
  const currentDay = today.getDay() // 0 = Sunday, 1 = Monday, etc.

  // Calculate days to subtract to get to Monday (if Sunday, go back 6 days)
  const daysToMonday = currentDay === 0 ? 6 : currentDay - 1

  // Create start date (Monday)
  const startDate = new Date(today)
  startDate.setDate(today.getDate() - daysToMonday)
  startDate.setHours(0, 0, 0, 0)

  // Create end date (Sunday)
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 6)
  endDate.setHours(0, 0, 0, 0)

  return {
    startDate,
    endDate,
  }
}

/**
 * Get the date range for the previous week (Monday to Sunday)
 * @returns Object with start and end dates for the previous week
 */
export function getPreviousWeekRange(): { startDate: Date; endDate: Date } {
  const { startDate } = getCurrentWeekRange()

  // Go back 7 days from the start of the current week
  const prevWeekStart = new Date(startDate)
  prevWeekStart.setDate(startDate.getDate() - 7)

  // End date is 6 days after start date
  const prevWeekEnd = new Date(prevWeekStart)
  prevWeekEnd.setDate(prevWeekStart.getDate() + 6)

  return {
    startDate: prevWeekStart,
    endDate: prevWeekEnd,
  }
}

/**
 * Get the date range for the current month
 * @returns Object with start and end dates for the current month
 */
export function getCurrentMonthRange(): { startDate: Date; endDate: Date } {
  const today = new Date()

  // First day of current month
  const startDate = new Date(today.getFullYear(), today.getMonth(), 1)
  startDate.setHours(0, 0, 0, 0)

  // Last day of current month
  const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
  endDate.setHours(0, 0, 0, 0)

  return {
    startDate,
    endDate,
  }
}

/**
 * Get the date range for the previous month
 * @returns Object with start and end dates for the previous month
 */
export function getPreviousMonthRange(): { startDate: Date; endDate: Date } {
  const today = new Date()

  // First day of previous month
  const startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1)
  startDate.setHours(0, 0, 0, 0)

  // Last day of previous month
  const endDate = new Date(today.getFullYear(), today.getMonth(), 0)
  endDate.setHours(0, 0, 0, 0)

  return {
    startDate,
    endDate,
  }
}

/**
 * Get the date range for the current year
 * @returns Object with start and end dates for the current year
 */
export function getCurrentYearRange(): { startDate: Date; endDate: Date } {
  const today = new Date()

  // First day of current year
  const startDate = new Date(today.getFullYear(), 0, 1)
  startDate.setHours(0, 0, 0, 0)

  // Last day of current year
  const endDate = new Date(today.getFullYear(), 11, 31)
  endDate.setHours(0, 0, 0, 0)

  return {
    startDate,
    endDate,
  }
}

/**
 * Get the date range for the previous year
 * @returns Object with start and end dates for the previous year
 */
export function getPreviousYearRange(): { startDate: Date; endDate: Date } {
  const today = new Date()

  // First day of previous year
  const startDate = new Date(today.getFullYear() - 1, 0, 1)
  startDate.setHours(0, 0, 0, 0)

  // Last day of previous year
  const endDate = new Date(today.getFullYear() - 1, 11, 31)
  endDate.setHours(0, 0, 0, 0)

  return {
    startDate,
    endDate,
  }
}
