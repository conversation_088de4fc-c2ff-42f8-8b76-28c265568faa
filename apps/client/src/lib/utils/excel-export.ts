import * as XLSX from 'xlsx'
import type { TimeRecord } from '~/components/time-record-editor'
import {
  formatDate,
  formatDurationFromMs,
  formatTime,
} from '~/lib/utils/date-time'

/**
 * Check if the app is running in a Tauri environment
 */
function isTauriApp(): boolean {
  return (
    typeof window !== 'undefined' &&
    window !== null &&
    '__TAURI_INTERNALS__' in window
  )
}

/**
 * Export time records to Excel
 * @param timeRecords Array of time records to export
 * @param title Title for the export (used in filename)
 * @param dateRange Date range string for the report
 */
export async function exportTimeRecordsToExcel(
  timeRecords: TimeRecord[],
  title: string,
  dateRange: string
): Promise<void> {
  // Create a new workbook
  const wb = XLSX.utils.book_new()

  // Format the data for Excel
  const data = timeRecords.map((record) => ({
    Date: formatDate(record.startTimestamp),
    Start: formatTime(record.startTimestamp),
    End: record.endTimestamp ? formatTime(record.endTimestamp) : '-',
    Duration: record.endTimestamp
      ? formatDurationFromMs(record.endTimestamp - record.startTimestamp)
      : '-',
    DurationInHours: record.endTimestamp
      ? (record.endTimestamp - record.startTimestamp) / 1000 / 60 / 60
      : 0,
    Project: record.task?.project?.name || '-',
    Task: record.task?.name || '-',
    Comment: record.comment || '-',
  }))

  // Add a summary row with total duration
  const totalDuration = timeRecords.reduce(
    (total, record) =>
      record.endTimestamp
        ? total + (record.endTimestamp - record.startTimestamp)
        : total,
    0
  )

  // Create a worksheet from the data
  const ws = XLSX.utils.json_to_sheet(data)

  // Add title and date range as merged cells at the top
  XLSX.utils.sheet_add_aoa(ws, [[`${title} - ${dateRange}`]], { origin: 'A1' })

  // Add total duration at the bottom
  const lastRow = data.length + 3 // +3 for header row, title row, and a blank row
  XLSX.utils.sheet_add_aoa(
    ws,
    [['Total Duration:', formatDurationFromMs(totalDuration)]],
    { origin: `A${lastRow}` }
  )

  // Set column widths
  const columnWidths = [
    { wch: 12 }, // Date
    { wch: 10 }, // Start
    { wch: 10 }, // End
    { wch: 10 }, // Duration
    { wch: 20 }, // Project
    { wch: 20 }, // Task
    { wch: 30 }, // Comment
  ]
  ws['!cols'] = columnWidths

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Time Records')

  // Generate filename based on title and date range
  const sanitizedTitle = title.replace(/[^a-z0-9]/gi, '_').toLowerCase()
  const sanitizedDateRange = dateRange.replace(/[^a-z0-9]/gi, '_').toLowerCase()
  const filename = `${sanitizedTitle}_${sanitizedDateRange}.xlsx`

  try {
    if (isTauriApp()) {
      // In Tauri app, use the native file dialog and file system API
      try {
        // Dynamically import Tauri APIs to avoid errors in browser environments
        const { save } = await import('@tauri-apps/plugin-dialog')
        const { writeFile } = await import('@tauri-apps/plugin-fs')

        // Convert the workbook to a binary buffer
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })

        // Show save dialog to let the user choose where to save the file
        const filePath = await save({
          filters: [
            {
              name: 'Excel Spreadsheet',
              extensions: ['xlsx'],
            },
          ],
          defaultPath: filename,
        })

        // If user selected a path, write the file
        if (filePath) {
          await writeFile(filePath, excelBuffer)
          console.log(`File saved to: ${filePath}`)
        }
      } catch (tauriError) {
        console.error('Error with Tauri APIs:', tauriError)
        // Fallback to browser method if Tauri APIs fail
        XLSX.writeFile(wb, filename)
      }
    } else {
      // In browser, use the standard XLSX.writeFile method
      XLSX.writeFile(wb, filename)
    }
  } catch (error) {
    console.error('Error exporting Excel file:', error)
    throw error
  }
}
