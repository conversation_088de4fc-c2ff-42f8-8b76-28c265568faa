import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  getNormalizationStrategy,
  minuteNormalization,
  noNormalization,
  normalizeTime,
} from './time-normalization'

describe('Time normalization utilities', () => {
  // Set up a fixed date for testing
  beforeEach(() => {
    // Mock the Date object to always return a fixed date
    const mockDate = new Date('2023-05-15T12:34:56.789Z')
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
  })

  afterEach(() => {
    // Restore the Date object
    vi.useRealTimers()
  })

  describe('noNormalization', () => {
    it('should return the timestamp unchanged', () => {
      const timestamp = Date.now()
      expect(noNormalization(timestamp)).toBe(timestamp)
    })
  })

  describe('minuteNormalization', () => {
    it('should round to the nearest 15 minutes by default', () => {
      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:30:00 (nearest 15-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:30:00.000Z').getTime()

      expect(minuteNormalization(timestamp, '')).toBe(expected)
    })

    it('should round to the nearest 5 minutes when specified', () => {
      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:35:00 (nearest 5-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:35:00.000Z').getTime()

      expect(minuteNormalization(timestamp, '5')).toBe(expected)
    })

    it('should round to the nearest 30 minutes when specified', () => {
      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:30:00 (nearest 30-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:30:00.000Z').getTime()

      expect(minuteNormalization(timestamp, '30')).toBe(expected)
    })

    it('should handle edge cases correctly', () => {
      // Test exactly on the boundary (12:30:00)
      const exactBoundary = new Date('2023-05-15T12:30:00.000Z').getTime()
      expect(minuteNormalization(exactBoundary, '15')).toBe(exactBoundary)

      // Test just before boundary (12:29:59.999)
      const justBeforeBoundary = new Date('2023-05-15T12:29:59.999Z').getTime()
      const expectedBefore = new Date('2023-05-15T12:30:00.000Z').getTime()
      expect(minuteNormalization(justBeforeBoundary, '15')).toBe(expectedBefore)

      // Test just after boundary (12:30:00.001)
      const justAfterBoundary = new Date('2023-05-15T12:30:00.001Z').getTime()
      const expectedAfter = new Date('2023-05-15T12:30:00.000Z').getTime()
      expect(minuteNormalization(justAfterBoundary, '15')).toBe(expectedAfter)
    })
  })

  describe('getNormalizationStrategy', () => {
    it('should return noNormalization for type NONE', () => {
      const strategy = getNormalizationStrategy('NONE')
      const timestamp = Date.now()
      expect(strategy(timestamp, '')).toBe(timestamp)
    })

    it('should return noNormalization for undefined type', () => {
      const strategy = getNormalizationStrategy(undefined)
      const timestamp = Date.now()
      expect(strategy(timestamp, '')).toBe(timestamp)
    })

    it('should return minuteNormalization for type MINUTE', () => {
      const strategy = getNormalizationStrategy('MINUTE')

      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:30:00 (nearest 15-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:30:00.000Z').getTime()

      expect(strategy(timestamp, '')).toBe(expected)
    })
  })

  describe('normalizeTime', () => {
    it('should apply no normalization for type NONE', () => {
      const timestamp = Date.now()
      expect(normalizeTime(timestamp, 'NONE')).toBe(timestamp)
    })

    it('should apply minute normalization for type MINUTE', () => {
      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:30:00 (nearest 15-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:30:00.000Z').getTime()

      expect(normalizeTime(timestamp, 'MINUTE')).toBe(expected)
    })

    it('should use the provided config value', () => {
      // Create a date at 12:34:56.789
      const date = new Date('2023-05-15T12:34:56.789Z')
      const timestamp = date.getTime()

      // Expected: 12:35:00 (nearest 5-minute increment to 12:34)
      const expected = new Date('2023-05-15T12:35:00.000Z').getTime()

      expect(normalizeTime(timestamp, 'MINUTE', '5')).toBe(expected)
    })

    it('should default to no normalization for unknown types', () => {
      const timestamp = Date.now()
      expect(normalizeTime(timestamp, 'UNKNOWN_TYPE')).toBe(timestamp)
    })
  })
})
