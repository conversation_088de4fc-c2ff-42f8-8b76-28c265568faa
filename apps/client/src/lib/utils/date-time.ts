/**
 * Utility functions for date and time operations
 */

/**
 * Format a timestamp to a date string
 */
export function formatDate(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleDateString('de-DE')
}

/**
 * Format a timestamp to a time string
 */
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  })
}

/**
 * Format a duration in milliseconds to a readable format
 */
export function formatDuration(
  startTimestamp: number,
  endTimestamp: number | null
): string {
  if (!endTimestamp) return '--'

  const durationMs = endTimestamp - startTimestamp

  const seconds = Math.floor((durationMs / 1000) % 60)
  const minutes = Math.floor((durationMs / (1000 * 60)) % 60)
  const hours = Math.floor(durationMs / (1000 * 60 * 60))

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

export function formatDurationFromMs(durationMs: number): string {
  const seconds = Math.floor((durationMs / 1000) % 60)
  const minutes = Math.floor((durationMs / (1000 * 60)) % 60)
  const hours = Math.floor(durationMs / (1000 * 60 * 60))

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * Calculate the start and end timestamps for a given date
 */
export function getDateRange(dateString: string): {
  selectedStart: number
  selectedEnd: number
} {
  const date = new Date(dateString)
  date.setHours(0, 0, 0, 0)
  return {
    selectedStart: date.getTime(),
    selectedEnd: date.getTime() + 86400000, // Add 24 hours in milliseconds
  }
}

/**
 * Get the previous day's date string in YYYY-MM-DD format
 */
export function getPreviousDay(dateString: string): string {
  const date = new Date(dateString)
  date.setDate(date.getDate() - 1)
  return date.toISOString().split('T')[0]
}

/**
 * Get the next day's date string in YYYY-MM-DD format
 */
export function getNextDay(dateString: string): string {
  const date = new Date(dateString)
  date.setDate(date.getDate() + 1)
  return date.toISOString().split('T')[0]
}

/**
 * Get the next day's date string in YYYY-MM-DD format
 */
export function getToday(): string {
  const date = new Date()
  return date.toISOString().split('T')[0]
}

/**
 * Get today's date in YYYY-MM-DD format
 */
export function getTodayDateString(): string {
  return new Date().toISOString().split('T')[0]
}
