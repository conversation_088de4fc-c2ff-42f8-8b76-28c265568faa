/**
 * Utility functions for keyboard event handling
 */

/**
 * Checks if the active element is an input element that should receive keyboard input
 * @returns true if an input element is focused, false otherwise
 */
export function isInputElementFocused(): boolean {
  const activeElement = document.activeElement
  if (
    activeElement &&
    (activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.tagName === 'SELECT' ||
      (activeElement as HTMLElement).hasAttribute('contenteditable'))
  ) {
    return true
  }
  return false
}

/**
 * Creates a keyboard event handler that only executes the callback if no input element is focused
 * @param callback The function to execute if no input element is focused
 * @returns A function that can be used as a keyboard event handler
 */
export function createGlobalKeyboardHandler<T extends KeyboardEvent>(
  callback: (e: T) => void
): (e: T) => void {
  return (e: T) => {
    if (isInputElementFocused()) {
      return
    }
    callback(e)
  }
}
