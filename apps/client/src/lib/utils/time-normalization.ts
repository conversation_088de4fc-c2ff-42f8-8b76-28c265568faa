/**
 * Time normalization strategies
 */

export const TIME_NORMALIZATION_OPTIONS = [
  { value: 'NONE', label: 'None' },
  { value: 'MINUTE', label: 'Round to minute' },
]

/**
 * Time normalization types
 */
export type TimeNormalizationType = 'NONE' | 'MINUTE'

/**
 * Interface for time normalization strategy
 */
export type TimeNormalizationStrategy = (
  timestamp: number,
  config: string
) => number

/**
 * No normalization - returns the timestamp as is
 */
export function noNormalization(timestamp: number): number {
  return timestamp
}

/**
 * Round to the nearest minute increment
 * @param timestamp The timestamp to normalize
 * @param config The number of minutes to round to (e.g., "15" for 15-minute increments)
 * @returns The normalized timestamp
 */
export function minuteNormalization(timestamp: number, config: string): number {
  const date = new Date(timestamp)
  const minutes = date.getMinutes()

  // Parse the config to get the minute increment
  const minuteIncrement = Number.parseInt(config, 10) || 15 // Default to 15 minutes if not specified

  // Calculate the nearest minute increment
  const roundedMinutes = Math.round(minutes / minuteIncrement) * minuteIncrement

  // Create a new date with the rounded minutes and reset seconds/ms
  date.setMinutes(roundedMinutes, 0, 0)

  return date.getTime()
}

/**
 * Get the appropriate normalization strategy based on the type
 * @param type The normalization type
 * @returns The normalization strategy function
 */
export function getNormalizationStrategy(
  type?: string
): TimeNormalizationStrategy {
  if (type === 'MINUTE') {
    return minuteNormalization
  }
  return noNormalization
}

/**
 * Normalize a timestamp based on the specified normalization type and config
 * @param timestamp The timestamp to normalize
 * @param type The normalization type
 * @param config The normalization configuration
 * @returns The normalized timestamp
 */
export function normalizeTime(
  timestamp: number,
  type?: string,
  config?: string
): number {
  const strategy = getNormalizationStrategy(type)
  return strategy(timestamp, config || '15')
}

export function normalizeOptionalTime(
  timestamp: number | undefined,
  type?: string,
  config?: string
): number | undefined {
  if (!timestamp) {
    return undefined
  }
  return normalizeTime(timestamp, type, config)
}
