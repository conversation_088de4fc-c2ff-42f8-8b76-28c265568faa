{"name": "@ftt/client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev:ui": "vite", "dev:zero-cache": "zero-cache-dev -p ../../packages/shared/src/schema.ts", "dev": "vite", "build": "tsc && vite build", "generate-routes": "tsr generate", "preview": "vite preview", "tauri": "tauri", "compile": "tsc --noEmit", "lint": "biome ci .", "lint:eslint": "eslint --ext .js,.jsx,.ts,.tsx src", "lint:all": "pnpm lint && pnpm lint:eslint", "format": "biome format --write .", "format:check": "biome format .", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@ftt/shared": "workspace:*", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@rocicorp/zero": "catalog:", "@tanstack/react-form": "^1.11.3", "@tanstack/react-query": "^5.76.2", "@tanstack/react-router": "^1.120.7", "@tanstack/react-table": "^8.21.3", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "~2.2.2", "@tauri-apps/plugin-fs": "~2.3.0", "@tauri-apps/plugin-shell": "~2", "@tauri-apps/plugin-window-state": "~2.2.2", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "es-toolkit": "^1.38.0", "jose": "^6.0.10", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "timescape": "^0.7.1", "uuidv7": "^1.0.2", "valibot": "^1.0.0", "vite-tsconfig-paths": "^5.1.4", "xlsx": "^0.18.5", "zod": "^3.25.23", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.27.0", "@simbathesailor/use-what-changed": "^2.0.0", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-router-devtools": "^1.120.7", "@tanstack/router-cli": "^1.120.7", "@tanstack/router-plugin": "^1.120.7", "@tauri-apps/cli": "^2.5.0", "@types/js-cookie": "^3.0.6", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.1.4", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "19.1.0-rc.2", "eslint-plugin-react-hooks": "6.0.0-rc1", "sass": "^1.89.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "vite": "^6.3.4", "vitest": "^3.1.4", "web-vitals": "^5.0.1"}, "trustedDependencies": ["@rocicorp/zero-sqlite3"], "overrides": {"esbuild": "0.25.0"}, "volta": {"node": "22.14.0"}}