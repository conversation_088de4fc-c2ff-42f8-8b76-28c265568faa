#!/bin/bash
# backup-db.sh - <PERSON><PERSON><PERSON> to export database content as <PERSON><PERSON><PERSON>, compress it, and rotate backups
# This script is designed to be run from a cron job

# Exit on error
set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
SERVER_DIR="$PROJECT_ROOT/apps/server"
EXPORT_DIR="$PROJECT_ROOT/data/export"
BACKUP_DIR="$PROJECT_ROOT/data/backups"
MAX_BACKUPS=10
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/db_backup_$TIMESTAMP.tar.gz"
LOG_FILE="$BACKUP_DIR/backup.log"

PNPM_DIR="/home/<USER>/.asdf/shims"

export PATH=$PATH:$PNPM_DIR
# Create directories if they don't exist
mkdir -p "$EXPORT_DIR"
mkdir -p "$BACKUP_DIR"

# Function to log messages
log() {
  echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to handle errors
handle_error() {
  log "ERROR: $1"
  exit 1
}

log "Starting database backup process..."

# Change to server directory
cd "$SERVER_DIR" || handle_error "Failed to change to server directory"

# Run the export-json script
log "Exporting database to JSON..."
pnpm export-json "$EXPORT_DIR" || handle_error "Failed to export database"

# Check if export was successful
if [ ! "$(ls -A "$EXPORT_DIR")" ]; then
  handle_error "Export directory is empty. Export may have failed."
fi

# Create tar.gz archive
log "Creating compressed archive..."
tar -czf "$BACKUP_FILE" -C "$PROJECT_ROOT/data" export || handle_error "Failed to create archive"

# Verify the archive was created
if [ ! -f "$BACKUP_FILE" ]; then
  handle_error "Backup file was not created"
fi

log "Backup created successfully: $BACKUP_FILE"

# Rotate backups - keep only the latest MAX_BACKUPS
log "Rotating backups (keeping latest $MAX_BACKUPS)..."
ls -t "$BACKUP_DIR"/db_backup_*.tar.gz | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm

# Count remaining backups
BACKUP_COUNT=$(ls "$BACKUP_DIR"/db_backup_*.tar.gz 2>/dev/null | wc -l)
log "Backup rotation complete. $BACKUP_COUNT backups remain."

log "Backup process completed successfully."
