meta {
  name: agg
  type: http
  seq: 4
}

post {
  url: {{serverUrl}}/api/timerecord-aggregate
  body: json
  auth: bearer
}

headers {
  accept: */*
  accept-language: en-US,en;q=0.9,de;q=0.8
  authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJjNTBiODA2OC0yM2Q4LTQ2ODYtOGYwYi1lMjJiZDE5YTVlY2QiLCJpYXQiOjE3NDYyMzM0MDYsImV4cCI6MTc0ODgyNTQwNn0.Rv5Wqs7yEuyyaYOLKomwLJD1loBQ39ogDJLyOnrnh4w
  cache-control: no-cache
  content-type: application/json
  dnt: 1
  origin: http://localhost:1420
  pragma: no-cache
  priority: u=1, i
  referer: http://localhost:1420/
  sec-ch-ua: "Chromium";v="135", "Not-A.Brand";v="8"
  sec-ch-ua-mobile: ?0
  sec-ch-ua-platform: "macOS"
  sec-fetch-dest: empty
  sec-fetch-mode: cors
  sec-fetch-site: cross-site
  user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
}

auth:bearer {
  token: {{token}}
}

body:json {
  {
    "startTimestamp": 1709247600000,
    "endTimestamp": 1767222000000,
    "customerIds": [
      "10c2fd80-37af-4a15-81dc-eaaec2cb50df"
    ]
  }
}
