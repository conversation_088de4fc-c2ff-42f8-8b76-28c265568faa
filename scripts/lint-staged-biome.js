#!/usr/bin/env node

/**
 * This script is used by lint-staged to run Biome on staged files.
 * It takes a list of file paths as arguments and runs Biome on them.
 *
 * Usage:
 *   node lint-staged-biome.js check file1.ts file2.ts
 *   node lint-staged-biome.js format file1.ts file2.ts
 */

const { spawnSync } = require('node:child_process')

// Get the command and files from the arguments
const [, , command, ...files] = process.argv

if (files.length === 0) {
  console.log('No files to process')
  process.exit(0)
}

console.log('Running Biome on the following files:', files)
// Build the command
let args = []
if (command === 'check') {
  args = ['biome', 'check', '--fix', ...files]
} else if (command === 'format') {
  args = ['biome', 'format', '--write', ...files]
} else {
  console.error(`Unknown command: ${command}`)
  process.exit(1)
}

try {
  // Run the command using pnpm
  const result = spawnSync('pnpm', args, {
    stdio: 'inherit',
    encoding: 'utf-8',
  })

  // Exit with the same code as the command
  process.exit(result.status || 0)
} catch (error) {
  console.error(`Error running Biome: ${error.message}`)
  process.exit(1)
}
