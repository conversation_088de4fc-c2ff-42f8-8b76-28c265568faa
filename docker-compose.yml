services:
  zstart_postgres:
    image: postgres:16.2-alpine
    shm_size: 1g
    user: postgres
    restart: always
    healthcheck:
      test: "pg_isready -U user --dbname=postgres"
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - 5430:5432
    environment:
      POSTGRES_USER: user
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: password
    command: |
      postgres
      -c wal_level=logical
      -c max_wal_senders=10
      -c max_replication_slots=5
      -c hot_standby=on
      -c hot_standby_feedback=on
    volumes:
      - ftt_pgdata:/var/lib/postgresql/data
      - ./docker/:/docker-entrypoint-initdb.d

  cache:
    image: rocicorp/zero:0.20.**********
    environment:
      ZERO_REPLICA_FILE: "/data/sync-replica.db"
      ZERO_UPSTREAM_DB: "***********************************************/postgres?sslmode=disable"
      ZERO_CVR_DB: "***********************************************/postgres?sslmode=disable"
      ZERO_CHANGE_DB: "***********************************************/postgres?sslmode=disable"
      ZERO_AUTH_SECRET: "secretkey"
      LOG_LEVEL: "debug"
    volumes:
      - ftt_zerodata:/data
    ports:
      - 4848:4848

  server:
    build: .
    ports:
      - 3011:3011
    environment:
      PORT: 3011
      ZERO_AUTH_SECRET: "secretkey"
      DATABASE_URL: "***********************************************/postgres"
      ZERO_UPSTREAM_DB: "***********************************************/postgres"
      NODE_ENV: "production"
    # Add a healthcheck to ensure the server is running properly
    healthcheck:
      test:
        ["CMD", "wget", "--spider", "-q", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  ftt_pgdata:
    driver: local
  ftt_zerodata:
    driver: local
