{"name": "@ftt/root", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "turbo test", "dev:server": "cd apps/server && pnpm dev", "dev:zero-cache": "zero-cache-dev -p src/schema.ts", "dev:db-up": "docker-compose up", "dev:db-down": "docker-compose down", "dev:clean": "source apps/client/.env && docker volume rm -f timetracker-tauri_ftt_pgdata && rm -rf \"${ZERO_REPLICA_FILE}\"*", "docker": "DOCKER_BUILDKIT=1 docker build -t rburgst/ftt-server .", "clean-all": "pnpm dev:db-down && pnpm dev:clean && pnpm dev:db-up", "import-json": "cd apps/server && pnpm import-json", "compile": "turbo compile", "lint": "turbo lint", "lint:eslint": "turbo lint:eslint", "lint:all": "turbo lint:all", "format": "turbo format", "format:check": "turbo format:check", "biome:check": "biome check .", "biome:lint": "biome lint .", "biome:format": "biome format --write .", "biome:format:check": "biome format .", "prepare": "husky", "lint-staged": "lint-staged", "commitlint": "commitlint --edit"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "volta": {"node": "22.15.0", "pnpm": "10.11.0"}, "trustedDependencies": ["@rocicorp/zero-sqlite3"], "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@rocicorp/zero-sqlite3", "@tailwindcss/oxide", "esbuild", "protobufjs"]}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "turbo": "^2.5.2"}, "lint-staged": {"*": ["biome check --no-errors-on-unmatched --files-ignore-unknown=true --write"]}}